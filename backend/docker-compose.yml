services:
  cloudflared:
    image: cloudflare/cloudflared:latest
    restart: unless-stopped
    command: tunnel run
    environment:
      - TUNNEL_TOKEN=eyJhIjoiYzdlNjdhZmJhNmVjNGNiZTgzOTU5MzdhZWVjNmIyYzMiLCJ0IjoiYjUzYThiOWItM2VlMC00NmQxLTlhYjAtYTgzOTZkMjE3NWQxIiwicyI6IllXVTVNakpqTTJJdE1tUXpZaTAwTmpabExXSTBZV0V0TldZME9URTFOekl6TXpFdyJ9
    networks:
      - tstack-network
  backend:
    build: .
    container_name: tstack-backend
    command: sh -c "npx prisma db push && npm run seed && npm run start:prod"
    ports:
      - '3001:3001'
    # depends_on:
    #   - db
    env_file:
      - .env
    environment:
      # Override DATABASE_URL for Docker (Mac/Windows)
      - DATABASE_URL=postgresql://dev:<EMAIL>:5432/property_trading?schema=public
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    networks:
      - tstack-network

networks:
  tstack-network:
    name: tstack-network
    external: true
