# Property Trading System Database Schema

## Overview
ระบบฐานข้อมูลสำหรับการซื้อขายทรัพย์สินผ่านการประมูล ออกแบบสำหรับ NestJS + PostgreSQL + Prisma

## Database Structure

### Master Data Tables (ตารางข้อมูลหลัก)

#### 1. Owner (ผู้ถือครองทรัพย์สิน)
- `ownerId` - Primary Key
- `ownerName` - ชื่อผู้ถือครอง
- `ownerTel` - เบอร์โทรศัพท์
- `ownerCode` - รหัสผู้ถือครอง

#### 2. Assessor (ผู้ประเมินราคาทรัพย์สิน)
- `assessorId` - Primary Key
- `assessorName` - ชื่อผู้ประเมิน
- `assessorTel` - เบอร์โทรศัพท์
- `assessorCode` - รหัสผู้ประเมิน

#### 3. AssetSta (สถานะทรัพย์สิน)
- `assetsStaId` - Primary Key
- `assetsStaName` - ชื่อสถานะ (ขายแล้ว/ยังไม่ขาย)
- `assetsStaCode` - รหัสสถานะ

#### 4. AssetType (ประเภททรัพย์สิน)
- `assetTypeId` - Primary Key
- `assetTypeName` - ชื่อประเภททรัพย์สิน
- `assetTypeCode` - รหัสประเภท

#### 5. Court (ศาล)
- `courtId` - Primary Key
- `courtName` - ชื่อศาล
- `courtAddress` - ที่อยู่ศาล
- `courtCode` - รหัสศาล

#### 6. CaseType (ประเภทคดี)
- `caseTypeId` - Primary Key
- `caseTypeName` - ชื่อประเภทคดี
- `caseTypeCode` - รหัสประเภทคดี

#### 7. CaseColor (สีของคดี)
- `caseColorId` - Primary Key
- `caseColorName` - ชื่อสี (แดง/ดำ)
- `caseColorCode` - รหัสสี

#### 8. Creditor (เจ้าหนี้)
- `credId` - Primary Key
- `credName` - ชื่อเจ้าหนี้
- `credTel` - เบอร์โทรศัพท์
- `credCode` - รหัสเจ้าหนี้

#### 9. Debtor (ลูกหนี้)
- `debtorId` - Primary Key
- `debtorName` - ชื่อลูกหนี้
- `debtorTel` - เบอร์โทรศัพท์
- `debtorCode` - รหัสลูกหนี้

#### 10. Employee (พนักงาน)
- `emId` - Primary Key
- `emName` - ชื่อพนักงาน
- `emTel` - เบอร์โทรศัพท์
- `emCode` - รหัสพนักงาน

#### 11. Customer (ลูกค้า)
- `cusId` - Primary Key
- `cusName` - ชื่อลูกค้า
- `cusTel` - เบอร์โทรศัพท์
- `cusCode` - รหัสลูกค้า

#### 12. AuctionSta (สถานะการประมูล)
- `auctionStaId` - Primary Key
- `auctionStaName` - ชื่อสถานะการประมูล
- `auctionStaCode` - รหัสสถานะ

#### 13. AssetGroupSta (สถานะกลุ่มทรัพย์สิน)
- `assgStaId` - Primary Key
- `assgStaName` - ชื่อสถานะกลุ่ม
- `assgStaCode` - รหัสสถานะ

#### 14. GroupType (ประเภทของกลุ่มทรัพย์สิน)
- `gtId` - Primary Key
- `gtName` - ชื่อประเภทกลุ่ม
- `gtCode` - รหัสประเภท

#### 15. AnnounceSta (สถานะการประกาศ)
- `anStaId` - Primary Key
- `anStaName` - ชื่อสถานะการประกาศ
- `anStaCode` - รหัสสถานะ

#### 16. AnnounceDetailSta (สถานะรายละเอียดการประกาศ)
- `andtStaId` - Primary Key
- `andtStaName` - ชื่อสถานะรายละเอียด
- `andtStaCode` - รหัสสถานะ

#### 17. Province (จังหวัด)
- `provinceId` - Primary Key
- `provinceName` - ชื่อจังหวัด
- `provinceCode` - รหัสจังหวัด

#### 18. District (อำเภอ)
- `districtId` - Primary Key
- `districtName` - ชื่ออำเภอ
- `districtCode` - รหัสอำเภอ
- `provinceId` - Foreign Key to Province

#### 19. Subdistrict (ตำบล)
- `subdistrictId` - Primary Key
- `subdistrictName` - ชื่อตำบล
- `subdistrictCode` - รหัสตำบล
- `districtId` - Foreign Key to District

### Transaction Tables (ตารางข้อมูลธุรกรรม)

#### 20. Case (คดี)
- `caseId` - Primary Key
- `caseNumber` - หมายเลขคดี
- `courtId` - Foreign Key to Court
- `credId` - Foreign Key to Creditor
- `debtorId` - Foreign Key to Debtor
- `caseTypeId` - Foreign Key to CaseType
- `caseSummary` - สรุปคดี
- `caseDate` - วันที่คดี
- `createdAt` - วันที่สร้าง
- `caseColorId` - Foreign Key to CaseColor

#### 21. Asset (ทรัพย์สิน)
- `assetId` - Primary Key
- `assetName` - ชื่อทรัพย์สิน
- `assetDetail` - รายละเอียดทรัพย์สิน
- `assetLocation` - ที่ตั้งทรัพย์สิน
- `assetTypeId` - Foreign Key to AssetType
- `ownerId` - Foreign Key to Owner
- `createdAt` - วันที่สร้าง
- `caseId` - Foreign Key to Case
- `em_id` - Foreign Key to Employee
- `assetStaId` - Foreign Key to AssetSta
- `subdistrictId` - Foreign Key to Subdistrict
- `districtId` - Foreign Key to District
- `provinceId` - Foreign Key to Province
- `assetRemark` - หมายเหตุ

#### 22. Estimate (ประเมินราคาทรัพย์สิน)
- `esId` - Primary Key
- `esPrice` - ราคาประเมิน
- `esDate` - วันที่ประเมิน
- `assessorId` - Foreign Key to Assessor
- `assetId` - Foreign Key to Asset
- `createdAt` - วันที่สร้าง

#### 23. AssetGroup (กลุ่มของทรัพย์สินที่นำมารวมกันก่อนขาย)
- `assgId` - Primary Key
- `assetId` - Foreign Key to Asset
- `gtId` - Foreign Key to GroupType
- `createdAt` - วันที่สร้าง
- `assgStaId` - Foreign Key to AssetGroupSta
- `assgDetail` - รายละเอียดกลุ่ม
- `assgRemark` - หมายเหตุ

#### 24. Announce (ประกาศ)
- `anId` - Primary Key
- `anTitle` - หัวข้อประกาศ
- `anDescription` - รายละเอียดประกาศ
- `createdAt` - วันที่สร้าง
- `anStaId` - Foreign Key to AnnounceSta
- `em_id` - Foreign Key to Employee
- `anRemark` - หมายเหตุ

#### 25. AnnounceDetail (รายละเอียดการประกาศ)
- `andtId` - Primary Key
- `assgId` - Foreign Key to AssetGroup
- `andtStaId` - Foreign Key to AnnounceDetailSta
- `createdAt` - วันที่สร้าง
- `anId` - Foreign Key to Announce

#### 26. Event (กิจกรรมการประมูลสามารถกำหนดได้หลายรอบต่อ 1 การประกาศ)
- `eventId` - Primary Key
- `eventDate` - วันที่จัดกิจกรรม
- `createdAt` - วันที่สร้าง
- `anId` - Foreign Key to Announce
- `eventRound` - รอบการประมูล

#### 27. Auction (ลูกค้าผู้ชนะการประมูลทรัพย์สิน)
- `auctionId` - Primary Key
- `auctionStaId` - Foreign Key to AuctionSta
- `assgId` - Foreign Key to AssetGroup
- `eventId` - Foreign Key to Event
- `createdAt` - วันที่สร้าง
- `emId` - Foreign Key to Employee
- `cusId` - Foreign Key to Customer
- `auctionPrice` - ราคาประมูล

## Setup Instructions

### 1. Database Configuration
```bash
# Update .env file with your PostgreSQL credentials
DATABASE_URL="postgresql://username:password@localhost:5432/property_trading?schema=public"
```

### 2. Run Migration
```bash
# If you have PostgreSQL running
npx prisma migrate dev

# Or apply the migration manually
npx prisma db push
```

### 3. Generate Prisma Client
```bash
npx prisma generate
```

### 4. Seed Data (Optional)
```bash
# Create seed script if needed
npx prisma db seed
```

## Usage Example

```typescript
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Create a new owner
const owner = await prisma.owner.create({
  data: {
    ownerName: 'John Doe',
    ownerTel: '0812345678',
    ownerCode: 'OWN001'
  }
})

// Create an asset
const asset = await prisma.asset.create({
  data: {
    assetName: 'House in Bangkok',
    assetDetail: 'Beautiful house with 3 bedrooms',
    assetLocation: 'Bangkok, Thailand',
    ownerId: owner.ownerId
  }
})
```

## Notes
- ลำดับการสร้างตารางได้ถูกจัดเรียงตาม dependency แล้ว
- ทุก Foreign Key ใช้ ON DELETE SET NULL เพื่อความปลอดภัย
- ใช้ SERIAL สำหรับ Primary Key (Auto increment)
- ใช้ VARCHAR แทน NVARCHAR สำหรับ PostgreSQL
- DateTime fields ใช้ TIMESTAMP(3) สำหรับ millisecond precision
