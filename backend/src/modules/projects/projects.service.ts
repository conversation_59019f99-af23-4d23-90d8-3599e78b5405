// projects.service.ts;
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';

@Injectable()
export class ProjectsService {
  constructor(private prisma: PrismaService) {}

  async create(dto: CreateProjectDto) {
    const data = {
      name: dto.name,
      description: dto.description,
    };

    return this.prisma.project.create({ data });
  }

  findAll(search?: string) {
    const where = search
      ? {
          name: { contains: search, mode: 'insensitive' as const },
        }
      : {};

    return this.prisma.projects.findMany({
      where,
      orderBy: { createdAt: 'desc' },
    });
  }

  findOne(id: number) {
    return this.prisma.projects.findUnique({ where: { id } });
  }

  update(id: number, dto: UpdateProjectDto) {
    return this.prisma.projects.update({ where: { id }, data: dto });
  }

  remove(id: number) {
    return this.prisma.projects.delete({ where: { id } });
  }
}
