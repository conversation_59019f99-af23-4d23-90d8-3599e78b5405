import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateStatusDto } from './dto/create-status.dto';
import { UpdateStatusDto } from './dto/update-status.dto';

@Injectable()
export class StatusService {
  constructor(private prisma: PrismaService) {}

  create(dto: CreateStatusDto) {
    return this.prisma.status.create({ data: dto });
  }

  findAll(search?: string) {
    const where = search
      ? {
          name: { contains: search, mode: 'insensitive' as const },
        }
      : {};

    return this.prisma.status.findMany({
      where,
      orderBy: { createdAt: 'desc' },
    });
  }

  findOne(id: number) {
    return this.prisma.status.findUnique({ where: { id } });
  }

  update(id: number, dto: UpdateStatusDto) {
    return this.prisma.status.update({ where: { id }, data: dto });
  }

  remove(id: number) {
    return this.prisma.status.delete({ where: { id } });
  }
}
