// src/modules/tasks/tasks.service.ts
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';

@Injectable()
export class TasksService {
  constructor(private prisma: PrismaService) {}

  create(dto: CreateTaskDto) {
    return this.prisma.tasks.create({
      data: dto,
      include: {
        project: { select: { id: true, name: true } },
        user: { select: { id: true, name: true } },
        status: { select: { id: true, name: true } },
      },
    });
  }

  findAll(search?: string) {
    const where = search
      ? {
          OR: [
            { name: { contains: search, mode: 'insensitive' as const } },
            {
              description: {
                contains: search,
                mode: 'insensitive' as const,
              },
            },
          ],
        }
      : {};

    return this.prisma.tasks.findMany({
      where,
      include: {
        project: { select: { id: true, name: true } },
        user: { select: { id: true, name: true } },
        status: { select: { id: true, name: true } },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  findOne(id: number) {
    return this.prisma.tasks.findUnique({ where: { id } });
  }

  update(id: number, dto: UpdateTaskDto) {
    return this.prisma.tasks.update({
      where: { id },
      data: dto,
      include: {
        project: { select: { id: true, name: true } },
        user: { select: { id: true, name: true } },
        status: { select: { id: true, name: true } },
      },
    });
  }

  remove(id: number) {
    return this.prisma.tasks.delete({ where: { id } });
  }
}
