import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { PrismaService } from './prisma/prisma.service';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { ValidationPipe } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // ✅ เปิด CORS
  app.enableCors({
    origin: [
      'http://localhost:3002',
      'http://frontend:3002',
      'https://calwage.com',
      'https://www.calwage.com',
    ],
    credentials: true,
  });

  // ✅ Global Validation Pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // ✅ Log  request  เข้ามา
  app.useGlobalInterceptors(new LoggingInterceptor());

  // ✅ เชื่อมต่อ DB
  const prisma = app.get(PrismaService);
  await prisma.$connect().catch(async () => {
    console.log('🟡 Waiting for DB to be ready...');
    await new Promise((res) => setTimeout(res, 3002));
    process.exit(1);
  });

  await app.listen(3001, '0.0.0.0');
  console.log('🚀 Backend ready on https://backend.calwage.com');
}
bootstrap();
