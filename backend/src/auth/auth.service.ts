// src/auth/auth.service.ts
import { Injectable } from '@nestjs/common';
import { UsersService } from '../modules/users/users.service';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
  ) {}

  async validateUser(email: string) {
    const user = await this.usersService.findByEmail(email);
    if (!user) return null;

    // ไม่ต้องตรวจสอบ password
    const token = this.jwtService.sign({ sub: user.id, email: user.email });

    return {
      token,
      user, // ไม่มี password อยู่แล้ว
    };
  }
}
