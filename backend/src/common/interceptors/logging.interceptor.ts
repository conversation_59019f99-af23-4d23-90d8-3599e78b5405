import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Observable, tap } from 'rxjs';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const req = context.switchToHttp().getRequest();
    const method = req.method;
    const url = req.url;
    const body = req.body;

    const now = Date.now();
    console.log(`➡️  ${method} ${url}`, body);

    return next
      .handle()
      .pipe(
        tap(() =>
          console.log(
            `⬅️  ${method} ${url} completed in ${Date.now() - now}ms`,
          ),
        ),
      );
  }
}
