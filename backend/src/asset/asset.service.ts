import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Asset, Prisma } from '@prisma/client';

@Injectable()
export class AssetService {
  constructor(private prisma: PrismaService) {}

  // Create a new asset
  async create(data: Prisma.AssetCreateInput): Promise<Asset> {
    return this.prisma.asset.create({
      data,
      include: {
        assetType: true,
        owner: true,
        case: true,
        employee: true,
        assetSta: true,
        subdistrict: {
          include: {
            district: {
              include: {
                province: true,
              },
            },
          },
        },
        district: true,
        province: true,
      },
    });
  }

  // Find all assets with pagination
  async findAll(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.AssetWhereUniqueInput;
    where?: Prisma.AssetWhereInput;
    orderBy?: Prisma.AssetOrderByWithRelationInput;
  }): Promise<Asset[]> {
    const { skip, take, cursor, where, orderBy } = params;
    return this.prisma.asset.findMany({
      skip,
      take,
      cursor,
      where,
      orderBy,
      include: {
        assetType: true,
        owner: true,
        case: true,
        employee: true,
        assetSta: true,
        subdistrict: {
          include: {
            district: {
              include: {
                province: true,
              },
            },
          },
        },
        district: true,
        province: true,
        assetGroups: true,
        estimates: {
          include: {
            assessor: true,
          },
        },
      },
    });
  }

  // Find a single asset by ID
  async findOne(assetId: number): Promise<Asset | null> {
    return this.prisma.asset.findUnique({
      where: { assetId },
      include: {
        assetType: true,
        owner: true,
        case: {
          include: {
            court: true,
            creditor: true,
            debtor: true,
            caseType: true,
            caseColor: true,
          },
        },
        employee: true,
        assetSta: true,
        subdistrict: {
          include: {
            district: {
              include: {
                province: true,
              },
            },
          },
        },
        district: true,
        province: true,
        assetGroups: {
          include: {
            groupType: true,
            assetGroupSta: true,
          },
        },
        estimates: {
          include: {
            assessor: true,
          },
          orderBy: {
            esDate: 'desc',
          },
        },
      },
    });
  }

  // Update an asset
  async update(params: {
    where: Prisma.AssetWhereUniqueInput;
    data: Prisma.AssetUpdateInput;
  }): Promise<Asset> {
    const { where, data } = params;
    return this.prisma.asset.update({
      data,
      where,
      include: {
        assetType: true,
        owner: true,
        case: true,
        employee: true,
        assetSta: true,
        subdistrict: {
          include: {
            district: {
              include: {
                province: true,
              },
            },
          },
        },
        district: true,
        province: true,
      },
    });
  }

  // Delete an asset
  async remove(where: Prisma.AssetWhereUniqueInput): Promise<Asset> {
    return this.prisma.asset.delete({
      where,
    });
  }

  // Find assets by case
  async findByCase(caseId: number): Promise<Asset[]> {
    return this.prisma.asset.findMany({
      where: { caseId },
      include: {
        assetType: true,
        owner: true,
        assetSta: true,
        estimates: {
          include: {
            assessor: true,
          },
          orderBy: {
            esDate: 'desc',
          },
          take: 1, // Get latest estimate
        },
      },
    });
  }

  // Find assets by owner
  async findByOwner(ownerId: number): Promise<Asset[]> {
    return this.prisma.asset.findMany({
      where: { ownerId },
      include: {
        assetType: true,
        case: true,
        assetSta: true,
        estimates: {
          include: {
            assessor: true,
          },
          orderBy: {
            esDate: 'desc',
          },
          take: 1, // Get latest estimate
        },
      },
    });
  }

  // Search assets by location
  async searchByLocation(searchTerm: string): Promise<Asset[]> {
    return this.prisma.asset.findMany({
      where: {
        OR: [
          {
            assetLocation: {
              contains: searchTerm,
              mode: 'insensitive',
            },
          },
          {
            province: {
              provinceName: {
                contains: searchTerm,
                mode: 'insensitive',
              },
            },
          },
          {
            district: {
              districtName: {
                contains: searchTerm,
                mode: 'insensitive',
              },
            },
          },
          {
            subdistrict: {
              subdistrictName: {
                contains: searchTerm,
                mode: 'insensitive',
              },
            },
          },
        ],
      },
      include: {
        assetType: true,
        owner: true,
        assetSta: true,
        province: true,
        district: true,
        subdistrict: true,
      },
    });
  }

  // Get asset statistics
  async getStatistics() {
    const totalAssets = await this.prisma.asset.count();
    const assetsByType = await this.prisma.asset.groupBy({
      by: ['assetTypeId'],
      _count: {
        assetId: true,
      },
      where: {
        assetTypeId: {
          not: null,
        },
      },
    });

    const assetsByStatus = await this.prisma.asset.groupBy({
      by: ['assetStaId'],
      _count: {
        assetId: true,
      },
      where: {
        assetStaId: {
          not: null,
        },
      },
    });

    return {
      totalAssets,
      assetsByType,
      assetsByStatus,
    };
  }
}
