import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { AssetService } from './asset.service';
import { Asset, Prisma } from '@prisma/client';

@Controller('assets')
export class AssetController {
  constructor(private readonly assetService: AssetService) {}

  @Post()
  async create(@Body() data: Prisma.AssetCreateInput): Promise<Asset> {
    return this.assetService.create(data);
  }

  @Get()
  async findAll(
    @Query('skip') skip?: string,
    @Query('take') take?: string,
    @Query('assetTypeId') assetTypeId?: string,
    @Query('assetStaId') assetStaId?: string,
    @Query('provinceId') provinceId?: string,
    @Query('orderBy') orderBy?: string,
  ): Promise<Asset[]> {
    const where: Prisma.AssetWhereInput = {};
    
    if (assetTypeId) {
      where.assetTypeId = parseInt(assetTypeId);
    }
    
    if (assetStaId) {
      where.assetStaId = parseInt(assetStaId);
    }
    
    if (provinceId) {
      where.provinceId = parseInt(provinceId);
    }
    
    let orderByObj: Prisma.AssetOrderByWithRelationInput = {};
    if (orderBy === 'name_asc') {
      orderByObj = { assetName: 'asc' };
    } else if (orderBy === 'name_desc') {
      orderByObj = { assetName: 'desc' };
    } else if (orderBy === 'date_asc') {
      orderByObj = { createdAt: 'asc' };
    } else if (orderBy === 'date_desc') {
      orderByObj = { createdAt: 'desc' };
    } else {
      orderByObj = { assetId: 'desc' };
    }
    
    return this.assetService.findAll({
      skip: skip ? parseInt(skip) : undefined,
      take: take ? parseInt(take) : undefined,
      where,
      orderBy: orderByObj,
    });
  }

  @Get('statistics')
  async getStatistics() {
    return this.assetService.getStatistics();
  }

  @Get('search')
  async search(@Query('term') term: string): Promise<Asset[]> {
    return this.assetService.searchByLocation(term);
  }

  @Get('by-case/:caseId')
  async findByCase(
    @Param('caseId', ParseIntPipe) caseId: number,
  ): Promise<Asset[]> {
    return this.assetService.findByCase(caseId);
  }

  @Get('by-owner/:ownerId')
  async findByOwner(
    @Param('ownerId', ParseIntPipe) ownerId: number,
  ): Promise<Asset[]> {
    return this.assetService.findByOwner(ownerId);
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<Asset | null> {
    return this.assetService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: Prisma.AssetUpdateInput,
  ): Promise<Asset> {
    return this.assetService.update({
      where: { assetId: id },
      data,
    });
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number): Promise<Asset> {
    return this.assetService.remove({ assetId: id });
  }
}
