import { Module } from '@nestjs/common';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './modules/users/users.module';

import { PrismaModule } from './prisma/prisma.module';
import { AssetModule } from './asset/asset.module';
import { AuctionModule } from './auction/auction.module';

@Module({
  imports: [
    PrismaModule,
    AuthModule,
    UsersModule,

    // Property Trading System Modules
    AssetModule,
    AuctionModule,
  ],
})
export class AppModule {}
