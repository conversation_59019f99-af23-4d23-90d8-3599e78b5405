import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { AuctionService } from './auction.service';
import { Auction, Prisma } from '@prisma/client';

@Controller('auctions')
export class AuctionController {
  constructor(private readonly auctionService: AuctionService) {}

  @Post()
  async create(@Body() data: Prisma.AuctionCreateInput): Promise<Auction> {
    return this.auctionService.create(data);
  }

  @Get()
  async findAll(
    @Query('skip') skip?: string,
    @Query('take') take?: string,
    @Query('auctionStaId') auctionStaId?: string,
    @Query('eventId') eventId?: string,
    @Query('cusId') cusId?: string,
    @Query('orderBy') orderBy?: string,
  ): Promise<Auction[]> {
    const where: Prisma.AuctionWhereInput = {};
    
    if (auctionStaId) {
      where.auctionStaId = parseInt(auctionStaId);
    }
    
    if (eventId) {
      where.eventId = parseInt(eventId);
    }
    
    if (cusId) {
      where.cusId = parseInt(cusId);
    }
    
    let orderByObj: Prisma.AuctionOrderByWithRelationInput = {};
    if (orderBy === 'price_asc') {
      orderByObj = { auctionPrice: 'asc' };
    } else if (orderBy === 'price_desc') {
      orderByObj = { auctionPrice: 'desc' };
    } else if (orderBy === 'date_asc') {
      orderByObj = { createdAt: 'asc' };
    } else if (orderBy === 'date_desc') {
      orderByObj = { createdAt: 'desc' };
    } else {
      orderByObj = { auctionId: 'desc' };
    }
    
    return this.auctionService.findAll({
      skip: skip ? parseInt(skip) : undefined,
      take: take ? parseInt(take) : undefined,
      where,
      orderBy: orderByObj,
    });
  }

  @Get('statistics')
  async getStatistics() {
    return this.auctionService.getStatistics();
  }

  @Get('winning')
  async findWinningAuctions(@Query('eventId') eventId?: string): Promise<Auction[]> {
    return this.auctionService.findWinningAuctions(
      eventId ? parseInt(eventId) : undefined,
    );
  }

  @Get('by-customer/:cusId')
  async findByCustomer(
    @Param('cusId', ParseIntPipe) cusId: number,
  ): Promise<Auction[]> {
    return this.auctionService.findByCustomer(cusId);
  }

  @Get('by-event/:eventId')
  async findByEvent(
    @Param('eventId', ParseIntPipe) eventId: number,
  ): Promise<Auction[]> {
    return this.auctionService.findByEvent(eventId);
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<Auction | null> {
    return this.auctionService.findOne(id);
  }

  @Patch(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() data: Prisma.AuctionUpdateInput,
  ): Promise<Auction> {
    return this.auctionService.update({
      where: { auctionId: id },
      data,
    });
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number): Promise<Auction> {
    return this.auctionService.remove({ auctionId: id });
  }
}
