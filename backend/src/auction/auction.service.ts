import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Auction, Prisma } from '@prisma/client';

@Injectable()
export class AuctionService {
  constructor(private prisma: PrismaService) {}

  // Create a new auction
  async create(data: Prisma.AuctionCreateInput): Promise<Auction> {
    return this.prisma.auction.create({
      data,
      include: {
        auctionSta: true,
        assetGroup: {
          include: {
            asset: {
              include: {
                assetType: true,
                owner: true,
              },
            },
            groupType: true,
            assetGroupSta: true,
          },
        },
        event: {
          include: {
            announce: true,
          },
        },
        employee: true,
        customer: true,
      },
    });
  }

  // Find all auctions with pagination
  async findAll(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.AuctionWhereUniqueInput;
    where?: Prisma.AuctionWhereInput;
    orderBy?: Prisma.AuctionOrderByWithRelationInput;
  }): Promise<Auction[]> {
    const { skip, take, cursor, where, orderBy } = params;
    return this.prisma.auction.findMany({
      skip,
      take,
      cursor,
      where,
      orderBy,
      include: {
        auctionSta: true,
        assetGroup: {
          include: {
            asset: {
              include: {
                assetType: true,
                owner: true,
                assetSta: true,
              },
            },
            groupType: true,
            assetGroupSta: true,
          },
        },
        event: {
          include: {
            announce: true,
          },
        },
        employee: true,
        customer: true,
      },
    });
  }

  // Find a single auction by ID
  async findOne(auctionId: number): Promise<Auction | null> {
    return this.prisma.auction.findUnique({
      where: { auctionId },
      include: {
        auctionSta: true,
        assetGroup: {
          include: {
            asset: {
              include: {
                assetType: true,
                owner: true,
                assetSta: true,
                case: {
                  include: {
                    court: true,
                    creditor: true,
                    debtor: true,
                  },
                },
                estimates: {
                  include: {
                    assessor: true,
                  },
                  orderBy: {
                    esDate: 'desc',
                  },
                  take: 1,
                },
              },
            },
            groupType: true,
            assetGroupSta: true,
          },
        },
        event: {
          include: {
            announce: {
              include: {
                announceSta: true,
                employee: true,
              },
            },
          },
        },
        employee: true,
        customer: true,
      },
    });
  }

  // Update an auction
  async update(params: {
    where: Prisma.AuctionWhereUniqueInput;
    data: Prisma.AuctionUpdateInput;
  }): Promise<Auction> {
    const { where, data } = params;
    return this.prisma.auction.update({
      data,
      where,
      include: {
        auctionSta: true,
        assetGroup: true,
        event: true,
        employee: true,
        customer: true,
      },
    });
  }

  // Delete an auction
  async remove(where: Prisma.AuctionWhereUniqueInput): Promise<Auction> {
    return this.prisma.auction.delete({
      where,
    });
  }

  // Find auctions by customer
  async findByCustomer(cusId: number): Promise<Auction[]> {
    return this.prisma.auction.findMany({
      where: { cusId },
      include: {
        auctionSta: true,
        assetGroup: {
          include: {
            asset: {
              include: {
                assetType: true,
              },
            },
          },
        },
        event: {
          include: {
            announce: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  // Find auctions by event
  async findByEvent(eventId: number): Promise<Auction[]> {
    return this.prisma.auction.findMany({
      where: { eventId },
      include: {
        auctionSta: true,
        assetGroup: {
          include: {
            asset: {
              include: {
                assetType: true,
                owner: true,
              },
            },
          },
        },
        customer: true,
        employee: true,
      },
      orderBy: {
        auctionPrice: 'desc',
      },
    });
  }

  // Find winning auctions (highest price per asset group)
  async findWinningAuctions(eventId?: number): Promise<Auction[]> {
    const where: Prisma.AuctionWhereInput = {};
    if (eventId) {
      where.eventId = eventId;
    }

    // Get all auctions grouped by asset group with max price
    const maxPriceAuctions = await this.prisma.auction.groupBy({
      by: ['assgId'],
      _max: {
        auctionPrice: true,
      },
      where,
    });

    // Get the actual auction records for the max prices
    const winningAuctions = await Promise.all(
      maxPriceAuctions.map(async (group) => {
        return this.prisma.auction.findFirst({
          where: {
            assgId: group.assgId,
            auctionPrice: group._max.auctionPrice,
            ...where,
          },
          include: {
            auctionSta: true,
            assetGroup: {
              include: {
                asset: {
                  include: {
                    assetType: true,
                    owner: true,
                  },
                },
                groupType: true,
              },
            },
            event: {
              include: {
                announce: true,
              },
            },
            customer: true,
            employee: true,
          },
        });
      }),
    );

    return winningAuctions.filter(Boolean) as Auction[];
  }

  // Get auction statistics
  async getStatistics() {
    const totalAuctions = await this.prisma.auction.count();
    
    const auctionsByStatus = await this.prisma.auction.groupBy({
      by: ['auctionStaId'],
      _count: {
        auctionId: true,
      },
      where: {
        auctionStaId: {
          not: null,
        },
      },
    });

    const totalAuctionValue = await this.prisma.auction.aggregate({
      _sum: {
        auctionPrice: true,
      },
      _avg: {
        auctionPrice: true,
      },
      _max: {
        auctionPrice: true,
      },
      _min: {
        auctionPrice: true,
      },
    });

    const auctionsByMonth = await this.prisma.auction.groupBy({
      by: ['createdAt'],
      _count: {
        auctionId: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 12,
    });

    return {
      totalAuctions,
      auctionsByStatus,
      totalAuctionValue,
      auctionsByMonth,
    };
  }
}
