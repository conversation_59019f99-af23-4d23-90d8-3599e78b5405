FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .

# Debug: Check copied files
RUN echo "=== Checking copied source files ===" && \
    ls -la src/ && \
    echo "=== Checking main.ts ===" && \
    cat src/main.ts && \
    echo "=== Checking package.json scripts ===" && \
    cat package.json | grep -A 10 '"scripts"'

# สร้าง Prisma Client
RUN npx prisma generate

# Try different build approaches
RUN echo "=== Trying NestJS build ===" && \
    npm run build || echo "NestJS build failed, trying TypeScript directly" && \
    echo "=== Trying TypeScript build ===" && \
    npx tsc || echo "TypeScript build failed" && \
    echo "=== Checking what was built ===" && \
    ls -la && \
    if [ -d "dist" ]; then \
        echo "=== Contents of dist ===" && \
        find dist -type f -name "*.js" | head -20; \
    else \
        echo "No dist directory found"; \
    fi && \
    echo "=== All JS files in project ===" && \
    find . -name "*.js" -type f | grep -E "(main|index)" | head -10

# Create a flexible entrypoint script
RUN echo '#!/bin/sh' > entrypoint.sh && \
    echo 'echo "📦 Running Prisma migrate deploy..."' >> entrypoint.sh && \
    echo 'npx prisma migrate deploy' >> entrypoint.sh && \
    echo 'echo "🌱 Running seed..."' >> entrypoint.sh && \
    echo 'npm run seed || echo "⚠️ Seed failed (may already exist)"' >> entrypoint.sh && \
    echo 'echo "🚀 Starting NestJS app..."' >> entrypoint.sh && \
    echo 'if [ -f "dist/main.js" ]; then' >> entrypoint.sh && \
    echo '  echo "📁 Running from dist/main.js"' >> entrypoint.sh && \
    echo '  node dist/main' >> entrypoint.sh && \
    echo 'elif [ -f "dist/src/main.js" ]; then' >> entrypoint.sh && \
    echo '  echo "📁 Running from dist/src/main.js"' >> entrypoint.sh && \
    echo '  node dist/src/main' >> entrypoint.sh && \
    echo 'else' >> entrypoint.sh && \
    echo '  echo "❌ No compiled main.js found, trying ts-node"' >> entrypoint.sh && \
    echo '  npx ts-node src/main.ts' >> entrypoint.sh && \
    echo 'fi' >> entrypoint.sh && \
    chmod +x ./entrypoint.sh

EXPOSE 3001
CMD ["./entrypoint.sh"]
