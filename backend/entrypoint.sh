#!/bin/sh

echo "📦 Running Prisma migrate deploy..."
npx prisma migrate deploy

echo "🌱 Running seed..."
npm run seed || echo "⚠️ Seed failed (may already exist)"

echo "🚀 Starting NestJS app..."
if [ -f "dist/main.js" ]; then
  echo "📁 Running from dist/main.js"
  node dist/main
elif [ -f "dist/src/main.js" ]; then
  echo "📁 Running from dist/src/main.js"
  node dist/src/main
else
  echo "❌ main.js not found!"
  exit 1
fi
