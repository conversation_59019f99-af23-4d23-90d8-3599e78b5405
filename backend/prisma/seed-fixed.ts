import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting seed with fixed IDs...');

  // Clear existing data first
  console.log('🗑️ Clearing existing data...');
  await prisma.auction.deleteMany();
  await prisma.event.deleteMany();
  await prisma.announceDetail.deleteMany();
  await prisma.announce.deleteMany();
  await prisma.assetGroup.deleteMany();
  await prisma.estimate.deleteMany();
  await prisma.asset.deleteMany();
  await prisma.case.deleteMany();
  await prisma.subdistrict.deleteMany();
  await prisma.district.deleteMany();
  await prisma.province.deleteMany();
  await prisma.announceDetailSta.deleteMany();
  await prisma.announceSta.deleteMany();
  await prisma.groupType.deleteMany();
  await prisma.assetGroupSta.deleteMany();
  await prisma.auctionSta.deleteMany();
  await prisma.customer.deleteMany();
  await prisma.employee.deleteMany();
  await prisma.debtor.deleteMany();
  await prisma.creditor.deleteMany();
  await prisma.caseColor.deleteMany();
  await prisma.caseType.deleteMany();
  await prisma.court.deleteMany();
  await prisma.assetType.deleteMany();
  await prisma.assetSta.deleteMany();
  await prisma.assessor.deleteMany();
  await prisma.owner.deleteMany();
  await prisma.user.deleteMany();

  // 1. User = ผู้ใช้งานระบบ
  console.log('Creating Users...');
  await prisma.user.createMany({
    data: [
      { id: 1, name: 'Admin User', email: '<EMAIL>', password: 'password123' },
      { id: 2, name: 'Manager User', email: '<EMAIL>', password: 'password123' },
      { id: 3, name: 'Staff User', email: '<EMAIL>', password: 'password123' },
    ],
  });

  // 2. Owner = ผู้ถือครองทรัพย์สิน
  console.log('Creating Owners...');
  await prisma.owner.createMany({
    data: [
      { ownerId: 1, ownerName: 'บริษัท สยามพาณิชย์ จำกัด', ownerTel: '02-123-4567', ownerCode: 'OWN001' },
      { ownerId: 2, ownerName: 'นายสมชาย ใจดี', ownerTel: '************', ownerCode: 'OWN002' },
      { ownerId: 3, ownerName: 'นางสาวสมหญิง รักดี', ownerTel: '************', ownerCode: 'OWN003' },
    ],
  });

  // 3. Assessor = ผู้ประเมินราคาทรัพย์สิน
  console.log('Creating Assessors...');
  await prisma.assessor.createMany({
    data: [
      { assessorId: 1, assessorName: 'นายประเมิน ชำนาญการ', assessorTel: '02-987-6543', assessorCode: 'ASS001' },
      { assessorId: 2, assessorName: 'นางสาวประเมิน เก่งมาก', assessorTel: '************', assessorCode: 'ASS002' },
    ],
  });

  // 4. AssetSta = สถานะ ขายแล้ว/ยังไม่ขาย
  console.log('Creating Asset Status...');
  await prisma.assetSta.createMany({
    data: [
      { assetsStaId: 1, assetsStaName: 'ยังไม่ขาย', assetsStaCode: 'NOT_SOLD' },
      { assetsStaId: 2, assetsStaName: 'ขายแล้ว', assetsStaCode: 'SOLD' },
      { assetsStaId: 3, assetsStaName: 'ระงับการขาย', assetsStaCode: 'SUSPENDED' },
    ],
  });

  // 5. AssetType = ประเภททรัพย์สิน
  console.log('Creating Asset Types...');
  await prisma.assetType.createMany({
    data: [
      { assetTypeId: 1, assetTypeName: 'ที่ดิน', assetTypeCode: 'LAND' },
      { assetTypeId: 2, assetTypeName: 'บ้านเดี่ยว', assetTypeCode: 'HOUSE' },
      { assetTypeId: 3, assetTypeName: 'คอนโดมิเนียม', assetTypeCode: 'CONDO' },
      { assetTypeId: 4, assetTypeName: 'อาคารพาณิชย์', assetTypeCode: 'COMMERCIAL' },
    ],
  });

  // 6. Court = ศาล
  console.log('Creating Courts...');
  await prisma.court.createMany({
    data: [
      { courtId: 1, courtName: 'ศาลแพ่งกรุงเทพใต้', courtAddress: '123 ถนนสาทร แขวงยานนาวา เขตสาทร กรุงเทพมหานคร 10120', courtCode: 'CRT001' },
      { courtId: 2, courtName: 'ศาลแพ่งธนบุรี', courtAddress: '456 ถนนกรุงธนบุรี แขวงคลองต้นไทร เขตคลองสาน กรุงเทพมหานคร 10600', courtCode: 'CRT002' },
    ],
  });

  // 7. CaseType = ประเภทคดี
  console.log('Creating Case Types...');
  await prisma.caseType.createMany({
    data: [
      { caseTypeId: 1, caseTypeName: 'คดีแพ่ง', caseTypeCode: 'CIVIL' },
      { caseTypeId: 2, caseTypeName: 'คดีล้มละลาย', caseTypeCode: 'BANKRUPTCY' },
      { caseTypeId: 3, caseTypeName: 'คดีบังคับคดี', caseTypeCode: 'EXECUTION' },
    ],
  });

  // 8. CaseColor = สีของคดี แดง/ดำ
  console.log('Creating Case Colors...');
  await prisma.caseColor.createMany({
    data: [
      { caseColorId: 1, caseColorName: 'คดีแดง', caseColorCode: 'RED' },
      { caseColorId: 2, caseColorName: 'คดีดำ', caseColorCode: 'BLACK' },
    ],
  });

  console.log('✅ Part 1 completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
