-- CreateTable
CREATE TABLE "owners" (
    "ownerId" SERIAL NOT NULL,
    "ownerName" VARCHAR(100) NOT NULL,
    "ownerTel" VARCHAR(20) NOT NULL,
    "ownerCode" VARCHAR(50),

    CONSTRAINT "owners_pkey" PRIMARY KEY ("ownerId")
);

-- CreateTable
CREATE TABLE "assessors" (
    "assessorId" SERIAL NOT NULL,
    "assessorName" VARCHAR(100) NOT NULL,
    "assessorTel" VARCHAR(20) NOT NULL,
    "assessorCode" VARCHAR(50),

    CONSTRAINT "assessors_pkey" PRIMARY KEY ("assessorId")
);

-- CreateTable
CREATE TABLE "assetSta" (
    "assetsStaId" SERIAL NOT NULL,
    "assetsStaName" VARCHAR(50),
    "assetsStaCode" VARCHAR(20),

    CONSTRAINT "assetSta_pkey" PRIMARY KEY ("assetsStaId")
);

-- CreateTable
CREATE TABLE "assetTypes" (
    "assetTypeId" SERIAL NOT NULL,
    "assetTypeName" VARCHAR(50) NOT NULL,
    "assetTypeCode" VARCHAR(30),

    CONSTRAINT "assetTypes_pkey" PRIMARY KEY ("assetTypeId")
);

-- CreateTable
CREATE TABLE "courts" (
    "courtId" SERIAL NOT NULL,
    "courtName" VARCHAR(100),
    "courtAddress" VARCHAR(300),
    "courtCode" VARCHAR(20),

    CONSTRAINT "courts_pkey" PRIMARY KEY ("courtId")
);

-- CreateTable
CREATE TABLE "caseTypes" (
    "caseTypeId" SERIAL NOT NULL,
    "caseTypeName" VARCHAR(50),
    "caseTypeCode" VARCHAR(20),

    CONSTRAINT "caseTypes_pkey" PRIMARY KEY ("caseTypeId")
);

-- CreateTable
CREATE TABLE "caseColors" (
    "caseColorId" SERIAL NOT NULL,
    "caseColorName" VARCHAR(50),
    "caseColorCode" VARCHAR(20),

    CONSTRAINT "caseColors_pkey" PRIMARY KEY ("caseColorId")
);

-- CreateTable
CREATE TABLE "creditors" (
    "credId" SERIAL NOT NULL,
    "credName" VARCHAR(100) NOT NULL,
    "credTel" VARCHAR(20),
    "credCode" VARCHAR(50),

    CONSTRAINT "creditors_pkey" PRIMARY KEY ("credId")
);

-- CreateTable
CREATE TABLE "debtors" (
    "debtorId" SERIAL NOT NULL,
    "debtorName" VARCHAR(100) NOT NULL,
    "debtorTel" VARCHAR(50) NOT NULL,
    "debtorCode" VARCHAR(50) NOT NULL,

    CONSTRAINT "debtors_pkey" PRIMARY KEY ("debtorId")
);

-- CreateTable
CREATE TABLE "employees" (
    "emId" SERIAL NOT NULL,
    "emName" VARCHAR(100) NOT NULL,
    "emTel" VARCHAR(20) NOT NULL,
    "emCode" VARCHAR(50),

    CONSTRAINT "employees_pkey" PRIMARY KEY ("emId")
);

-- CreateTable
CREATE TABLE "customers" (
    "cusId" SERIAL NOT NULL,
    "cusName" VARCHAR(100) NOT NULL,
    "cusTel" VARCHAR(20) NOT NULL,
    "cusCode" VARCHAR(50),

    CONSTRAINT "customers_pkey" PRIMARY KEY ("cusId")
);

-- CreateTable
CREATE TABLE "auctionSta" (
    "auctionStaId" SERIAL NOT NULL,
    "auctionStaName" VARCHAR(50),
    "auctionStaCode" VARCHAR(50),

    CONSTRAINT "auctionSta_pkey" PRIMARY KEY ("auctionStaId")
);

-- CreateTable
CREATE TABLE "assetGroupSta" (
    "assgStaId" SERIAL NOT NULL,
    "assgStaName" VARCHAR(50) NOT NULL,
    "assgStaCode" VARCHAR(20),

    CONSTRAINT "assetGroupSta_pkey" PRIMARY KEY ("assgStaId")
);

-- CreateTable
CREATE TABLE "groupTypes" (
    "gtId" SERIAL NOT NULL,
    "gtName" VARCHAR(20),
    "gtCode" VARCHAR(20),

    CONSTRAINT "groupTypes_pkey" PRIMARY KEY ("gtId")
);

-- CreateTable
CREATE TABLE "announceSta" (
    "anStaId" SERIAL NOT NULL,
    "anStaName" VARCHAR(50) NOT NULL,
    "anStaCode" VARCHAR(20),

    CONSTRAINT "announceSta_pkey" PRIMARY KEY ("anStaId")
);

-- CreateTable
CREATE TABLE "announceDetailSta" (
    "andtStaId" SERIAL NOT NULL,
    "andtStaName" VARCHAR(50) NOT NULL,
    "andtStaCode" VARCHAR(20),

    CONSTRAINT "announceDetailSta_pkey" PRIMARY KEY ("andtStaId")
);

-- CreateTable
CREATE TABLE "provinces" (
    "provinceId" SERIAL NOT NULL,
    "provinceName" VARCHAR(100),
    "provinceCode" VARCHAR(20),

    CONSTRAINT "provinces_pkey" PRIMARY KEY ("provinceId")
);

-- CreateTable
CREATE TABLE "districts" (
    "districtId" SERIAL NOT NULL,
    "districtName" VARCHAR(80),
    "districtCode" VARCHAR(20),
    "provinceId" INTEGER,

    CONSTRAINT "districts_pkey" PRIMARY KEY ("districtId")
);

-- CreateTable
CREATE TABLE "subdistricts" (
    "subdistrictId" SERIAL NOT NULL,
    "subdistrictName" VARCHAR(80),
    "subdistrictCode" VARCHAR(20),
    "districtId" INTEGER,

    CONSTRAINT "subdistricts_pkey" PRIMARY KEY ("subdistrictId")
);

-- CreateTable
CREATE TABLE "cases" (
    "caseId" SERIAL NOT NULL,
    "caseNumber" VARCHAR(50) NOT NULL,
    "courtId" INTEGER,
    "credId" INTEGER,
    "debtorId" INTEGER,
    "caseTypeId" INTEGER,
    "caseSummary" VARCHAR(500),
    "caseDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "caseColorId" INTEGER,

    CONSTRAINT "cases_pkey" PRIMARY KEY ("caseId")
);

-- CreateTable
CREATE TABLE "assets" (
    "assetId" SERIAL NOT NULL,
    "assetName" VARCHAR(100),
    "assetDetail" VARCHAR(500),
    "assetLocation" VARCHAR(200),
    "assetTypeId" INTEGER,
    "ownerId" INTEGER,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "caseId" INTEGER,
    "em_id" INTEGER,
    "assetStaId" INTEGER,
    "subdistrictId" INTEGER,
    "districtId" INTEGER,
    "provinceId" INTEGER,
    "assetRemark" VARCHAR(200),

    CONSTRAINT "assets_pkey" PRIMARY KEY ("assetId")
);

-- CreateTable
CREATE TABLE "estimates" (
    "esId" SERIAL NOT NULL,
    "esPrice" DOUBLE PRECISION,
    "esDate" TIMESTAMP(3),
    "assessorId" INTEGER,
    "assetId" INTEGER,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "estimates_pkey" PRIMARY KEY ("esId")
);

-- CreateTable
CREATE TABLE "assetGroups" (
    "assgId" SERIAL NOT NULL,
    "assetId" INTEGER,
    "gtId" INTEGER,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "assgStaId" INTEGER,
    "assgDetail" VARCHAR(500),
    "assgRemark" VARCHAR(200),

    CONSTRAINT "assetGroups_pkey" PRIMARY KEY ("assgId")
);

-- CreateTable
CREATE TABLE "announces" (
    "anId" SERIAL NOT NULL,
    "anTitle" VARCHAR(100),
    "anDescription" VARCHAR(500),
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "anStaId" INTEGER,
    "em_id" INTEGER,
    "anRemark" VARCHAR(300),

    CONSTRAINT "announces_pkey" PRIMARY KEY ("anId")
);

-- CreateTable
CREATE TABLE "announceDetails" (
    "andtId" SERIAL NOT NULL,
    "assgId" INTEGER,
    "andtStaId" INTEGER,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "anId" INTEGER,

    CONSTRAINT "announceDetails_pkey" PRIMARY KEY ("andtId")
);

-- CreateTable
CREATE TABLE "events" (
    "eventId" SERIAL NOT NULL,
    "eventDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "anId" INTEGER,
    "eventRound" INTEGER,

    CONSTRAINT "events_pkey" PRIMARY KEY ("eventId")
);

-- CreateTable
CREATE TABLE "auctions" (
    "auctionId" SERIAL NOT NULL,
    "auctionStaId" INTEGER,
    "assgId" INTEGER,
    "eventId" INTEGER,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "emId" INTEGER,
    "cusId" INTEGER,
    "auctionPrice" DOUBLE PRECISION,

    CONSTRAINT "auctions_pkey" PRIMARY KEY ("auctionId")
);

-- AddForeignKey
ALTER TABLE "districts" ADD CONSTRAINT "districts_provinceId_fkey" FOREIGN KEY ("provinceId") REFERENCES "provinces"("provinceId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "subdistricts" ADD CONSTRAINT "subdistricts_districtId_fkey" FOREIGN KEY ("districtId") REFERENCES "districts"("districtId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "cases" ADD CONSTRAINT "cases_courtId_fkey" FOREIGN KEY ("courtId") REFERENCES "courts"("courtId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "cases" ADD CONSTRAINT "cases_credId_fkey" FOREIGN KEY ("credId") REFERENCES "creditors"("credId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "cases" ADD CONSTRAINT "cases_debtorId_fkey" FOREIGN KEY ("debtorId") REFERENCES "debtors"("debtorId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "cases" ADD CONSTRAINT "cases_caseTypeId_fkey" FOREIGN KEY ("caseTypeId") REFERENCES "caseTypes"("caseTypeId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "cases" ADD CONSTRAINT "cases_caseColorId_fkey" FOREIGN KEY ("caseColorId") REFERENCES "caseColors"("caseColorId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assets" ADD CONSTRAINT "assets_assetTypeId_fkey" FOREIGN KEY ("assetTypeId") REFERENCES "assetTypes"("assetTypeId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assets" ADD CONSTRAINT "assets_ownerId_fkey" FOREIGN KEY ("ownerId") REFERENCES "owners"("ownerId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assets" ADD CONSTRAINT "assets_caseId_fkey" FOREIGN KEY ("caseId") REFERENCES "cases"("caseId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assets" ADD CONSTRAINT "assets_em_id_fkey" FOREIGN KEY ("em_id") REFERENCES "employees"("emId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assets" ADD CONSTRAINT "assets_assetStaId_fkey" FOREIGN KEY ("assetStaId") REFERENCES "assetSta"("assetsStaId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assets" ADD CONSTRAINT "assets_subdistrictId_fkey" FOREIGN KEY ("subdistrictId") REFERENCES "subdistricts"("subdistrictId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assets" ADD CONSTRAINT "assets_districtId_fkey" FOREIGN KEY ("districtId") REFERENCES "districts"("districtId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assets" ADD CONSTRAINT "assets_provinceId_fkey" FOREIGN KEY ("provinceId") REFERENCES "provinces"("provinceId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "estimates" ADD CONSTRAINT "estimates_assessorId_fkey" FOREIGN KEY ("assessorId") REFERENCES "assessors"("assessorId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "estimates" ADD CONSTRAINT "estimates_assetId_fkey" FOREIGN KEY ("assetId") REFERENCES "assets"("assetId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assetGroups" ADD CONSTRAINT "assetGroups_assetId_fkey" FOREIGN KEY ("assetId") REFERENCES "assets"("assetId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assetGroups" ADD CONSTRAINT "assetGroups_gtId_fkey" FOREIGN KEY ("gtId") REFERENCES "groupTypes"("gtId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assetGroups" ADD CONSTRAINT "assetGroups_assgStaId_fkey" FOREIGN KEY ("assgStaId") REFERENCES "assetGroupSta"("assgStaId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "announces" ADD CONSTRAINT "announces_anStaId_fkey" FOREIGN KEY ("anStaId") REFERENCES "announceSta"("anStaId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "announces" ADD CONSTRAINT "announces_em_id_fkey" FOREIGN KEY ("em_id") REFERENCES "employees"("emId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "announceDetails" ADD CONSTRAINT "announceDetails_assgId_fkey" FOREIGN KEY ("assgId") REFERENCES "assetGroups"("assgId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "announceDetails" ADD CONSTRAINT "announceDetails_andtStaId_fkey" FOREIGN KEY ("andtStaId") REFERENCES "announceDetailSta"("andtStaId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "announceDetails" ADD CONSTRAINT "announceDetails_anId_fkey" FOREIGN KEY ("anId") REFERENCES "announces"("anId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "events" ADD CONSTRAINT "events_anId_fkey" FOREIGN KEY ("anId") REFERENCES "announces"("anId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "auctions" ADD CONSTRAINT "auctions_auctionStaId_fkey" FOREIGN KEY ("auctionStaId") REFERENCES "auctionSta"("auctionStaId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "auctions" ADD CONSTRAINT "auctions_assgId_fkey" FOREIGN KEY ("assgId") REFERENCES "assetGroups"("assgId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "auctions" ADD CONSTRAINT "auctions_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES "events"("eventId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "auctions" ADD CONSTRAINT "auctions_emId_fkey" FOREIGN KEY ("emId") REFERENCES "employees"("emId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "auctions" ADD CONSTRAINT "auctions_cusId_fkey" FOREIGN KEY ("cusId") REFERENCES "customers"("cusId") ON DELETE SET NULL ON UPDATE CASCADE;
