// Property Trading System - Prisma Schema
// NestJS + PostgreSQL

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============= Master Data Tables (ตารางข้อมูลหลัก) =============

// User = ผู้ใช้งานระบบ
model User {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(100)
  email     String   @unique @db.VarChar(100)
  password  String   @db.VarChar(255)
  createdAt DateTime @default(now())

  @@map("users")
}



// Owner = ผู้ถือครองทรัพย์สิน
model Owner {
  ownerId   Int    @id @default(autoincrement())
  ownerName String @db.VarChar(100)
  ownerTel  String @db.VarChar(20)
  ownerCode String? @db.VarChar(50)

  // Relations
  assets Asset[]

  @@map("owners")
}

// Assessor = ผู้ประเมินราคาทรัพย์สิน
model Assessor {
  assessorId   Int    @id @default(autoincrement())
  assessorName String @db.Var<PERSON>har(100)
  assessorTel  String @db.VarChar(20)
  assessorCode String? @db.VarChar(50)

  // Relations
  estimates Estimate[]

  @@map("assessors")
}

// AssetSta = สถานะ ขายแล้ว/ยังไม่ขาย
model AssetSta {
  assetsStaId   Int     @id @default(autoincrement())
  assetsStaName String? @db.VarChar(50)
  assetsStaCode String? @db.VarChar(20)

  // Relations
  assets Asset[]

  @@map("assetSta")
}

// AssetType = ประเภททรัพย์สิน
model AssetType {
  assetTypeId   Int     @id @default(autoincrement())
  assetTypeName String  @db.VarChar(50)
  assetTypeCode String? @db.VarChar(30)

  // Relations
  assets Asset[]

  @@map("assetTypes")
}

// Court = ศาล
model Court {
  courtId      Int     @id @default(autoincrement())
  courtName    String? @db.VarChar(100)
  courtAddress String? @db.VarChar(300)
  courtCode    String? @db.VarChar(20)

  // Relations
  cases Case[]

  @@map("courts")
}

// CaseType = ประเภทคดี
model CaseType {
  caseTypeId   Int     @id @default(autoincrement())
  caseTypeName String? @db.VarChar(50)
  caseTypeCode String? @db.VarChar(20)

  // Relations
  cases Case[]

  @@map("caseTypes")
}

// CaseColor = สีของคดี แดง/ดำ
model CaseColor {
  caseColorId   Int     @id @default(autoincrement())
  caseColorName String? @db.VarChar(50)
  caseColorCode String? @db.VarChar(20)

  // Relations
  cases Case[]

  @@map("caseColors")
}

// Creditor = เจ้าหนี้
model Creditor {
  credId   Int     @id @default(autoincrement())
  credName String  @db.VarChar(100)
  credTel  String? @db.VarChar(20)
  credCode String? @db.VarChar(50)

  // Relations
  cases Case[]

  @@map("creditors")
}

// Debtor = ลูกหนี้
model Debtor {
  debtorId   Int    @id @default(autoincrement())
  debtorName String @db.VarChar(100)
  debtorTel  String @db.VarChar(50)
  debtorCode String @db.VarChar(50)

  // Relations
  cases Case[]

  @@map("debtors")
}

// Employee = พนักงาน
model Employee {
  emId   Int     @id @default(autoincrement())
  emName String  @db.VarChar(100)
  emTel  String  @db.VarChar(20)
  emCode String? @db.VarChar(50)

  // Relations
  assets    Asset[]
  announces Announce[]
  auctions  Auction[]

  @@map("employees")
}

// Customer = ลูกค้า
model Customer {
  cusId   Int     @id @default(autoincrement())
  cusName String  @db.VarChar(100)
  cusTel  String  @db.VarChar(20)
  cusCode String? @db.VarChar(50)

  // Relations
  auctions Auction[]

  @@map("customers")
}

// AuctionSta = สถานะการประมูล
model AuctionSta {
  auctionStaId   Int     @id @default(autoincrement())
  auctionStaName String? @db.VarChar(50)
  auctionStaCode String? @db.VarChar(50)

  // Relations
  auctions Auction[]

  @@map("auctionSta")
}

// AssetGroupSta = สถานะกลุ่มทรัพย์สิน
model AssetGroupSta {
  assgStaId   Int    @id @default(autoincrement())
  assgStaName String @db.VarChar(50)
  assgStaCode String? @db.VarChar(20)
  
  // Relations
  assetGroups AssetGroup[]

  @@map("assetGroupSta")
}

// GroupType = ประเภทของกลุ่มทรัพย์สิน
model GroupType {
  gtId   Int     @id @default(autoincrement())
  gtName String? @db.VarChar(20)
  gtCode String? @db.VarChar(20)

  // Relations
  assetGroups AssetGroup[]

  @@map("groupTypes")
}

// AnnounceSta = สถานะการประกาศ
model AnnounceSta {
  anStaId   Int    @id @default(autoincrement())
  anStaName String @db.VarChar(50)
  anStaCode String? @db.VarChar(20)

  // Relations
  announces Announce[]

  @@map("announceSta")
}

// AnnounceDetailSta = สถานะรายละเอียดการประกาศ
model AnnounceDetailSta {
  andtStaId   Int    @id @default(autoincrement())
  andtStaName String @db.VarChar(50)
  andtStaCode String? @db.VarChar(20)

  // Relations
  announceDetails AnnounceDetail[]

  @@map("announceDetailSta")
}

// Province = จังหวัด
model Province {
  provinceId   Int     @id @default(autoincrement())
  provinceName String? @db.VarChar(100)
  provinceCode String? @db.VarChar(20)

  // Relations
  districts District[]
  assets    Asset[]

  @@map("provinces")
}

// District = อำเภอ
model District {
  districtId   Int     @id @default(autoincrement())
  districtName String? @db.VarChar(80)
  districtCode String? @db.VarChar(20)
  provinceId   Int?

  // Relations
  province      Province?     @relation(fields: [provinceId], references: [provinceId])
  subdistricts  Subdistrict[]
  assets        Asset[]

  @@map("districts")
}

// Subdistrict = ตำบล
model Subdistrict {
  subdistrictId   Int     @id @default(autoincrement())
  subdistrictName String? @db.VarChar(80)
  subdistrictCode String? @db.VarChar(20)
  districtId      Int?

  // Relations
  district District? @relation(fields: [districtId], references: [districtId])
  assets   Asset[]

  @@map("subdistricts")
}

// ============= Transaction Tables (ตารางข้อมูลธุรกรรม) =============

// Case = คดี
model Case {
  caseId      Int       @id @default(autoincrement())
  caseNumber  String    @db.VarChar(50)
  courtId     Int?
  credId      Int?
  debtorId    Int?
  caseTypeId  Int?
  caseSummary String?   @db.VarChar(500)
  caseDate    DateTime?
  createdAt   DateTime? @default(now())
  caseColorId Int?

  // Relations
  court     Court?     @relation(fields: [courtId], references: [courtId])
  creditor  Creditor?  @relation(fields: [credId], references: [credId])
  debtor    Debtor?    @relation(fields: [debtorId], references: [debtorId])
  caseType  CaseType?  @relation(fields: [caseTypeId], references: [caseTypeId])
  caseColor CaseColor? @relation(fields: [caseColorId], references: [caseColorId])
  assets    Asset[]

  @@map("cases")
}

// Asset = ทรัพย์สิน
model Asset {
  assetId       Int       @id @default(autoincrement())
  assetName     String?   @db.VarChar(100)
  assetDetail   String?   @db.VarChar(500)
  assetLocation String?   @db.VarChar(200)
  assetTypeId   Int?
  ownerId       Int?
  createdAt     DateTime? @default(now())
  caseId        Int?
  em_id         Int?
  assetStaId    Int?
  subdistrictId Int?
  districtId    Int?
  provinceId    Int?
  assetRemark   String?   @db.VarChar(200)

  // Relations
  assetType   AssetType?   @relation(fields: [assetTypeId], references: [assetTypeId])
  owner       Owner?       @relation(fields: [ownerId], references: [ownerId])
  case        Case?        @relation(fields: [caseId], references: [caseId])
  employee    Employee?    @relation(fields: [em_id], references: [emId])
  assetSta    AssetSta?    @relation(fields: [assetStaId], references: [assetsStaId])
  subdistrict Subdistrict? @relation(fields: [subdistrictId], references: [subdistrictId])
  district    District?    @relation(fields: [districtId], references: [districtId])
  province    Province?    @relation(fields: [provinceId], references: [provinceId])

  // Other relations
  assetGroups AssetGroup[]
  estimates   Estimate[]

  @@map("assets")
}

// Estimate = ประเมินราคาทรัพย์สิน
model Estimate {
  esId        Int       @id @default(autoincrement())
  esPrice     Float?
  esDate      DateTime?
  assessorId  Int?
  assetId     Int?
  createdAt   DateTime? @default(now())

  // Relations
  assessor Assessor? @relation(fields: [assessorId], references: [assessorId])
  asset    Asset?    @relation(fields: [assetId], references: [assetId])

  @@map("estimates")
}

// AssetGroup = กลุ่มของทรัพย์สินที่นำมารวมกันก่อนขาย
model AssetGroup {
  assgId      Int       @id @default(autoincrement())
  assetId     Int?
  gtId        Int?
  createdAt   DateTime? @default(now())
  assgStaId   Int?
  assgDetail  String?   @db.VarChar(500)
  assgRemark  String?   @db.VarChar(200)

  // Relations
  asset         Asset?         @relation(fields: [assetId], references: [assetId])
  groupType     GroupType?     @relation(fields: [gtId], references: [gtId])
  assetGroupSta AssetGroupSta? @relation(fields: [assgStaId], references: [assgStaId])

  // Other relations
  announceDetails AnnounceDetail[]
  auctions        Auction[]

  @@map("assetGroups")
}

// Announce = ประกาศ
model Announce {
  anId          Int       @id @default(autoincrement()) 
  anTitle       String?   @db.VarChar(100) // หัวเรื่องประกาศ
  anDescription String?   @db.VarChar(500) // รายละเอียด
  createdAt     DateTime? @default(now())
  anStaId       Int? // 
  em_id         Int?
  anRemark      String?   @db.VarChar(300) // หมายเหตุ

  // Relations
  announceSta    AnnounceSta? @relation(fields: [anStaId], references: [anStaId])
  employee       Employee?    @relation(fields: [em_id], references: [emId])

  // Other relations
  announceDetails AnnounceDetail[]
  events          Event[]

  @@map("announces")
}

// AnnounceDetail = รายละเอียดการประกาศ
model AnnounceDetail {
  andtId    Int       @id @default(autoincrement())
  assgId    Int?
  andtStaId Int?
  createdAt DateTime? @default(now())
  anId      Int?

  // Relations
  assetGroup         AssetGroup?         @relation(fields: [assgId], references: [assgId])
  announceDetailSta  AnnounceDetailSta?  @relation(fields: [andtStaId], references: [andtStaId])
  announce           Announce?           @relation(fields: [anId], references: [anId])

  @@map("announceDetails")
}

// Event = กิจกรรมการประมูลสามารถกำหนดได้หลายรอบต่อ 1 การประกาศ
model Event {
  eventId    Int       @id @default(autoincrement())
  eventDate  DateTime?
  createdAt  DateTime? @default(now())
  anId       Int?
  eventRound Int?

  // Relations
  announce Announce? @relation(fields: [anId], references: [anId])

  // Other relations
  auctions Auction[]

  @@map("events")
}

// Auction = ลูกค้าผู้ชนะการประมูลทรัพย์สิน
model Auction {
  auctionId    Int       @id @default(autoincrement())
  auctionStaId Int?
  assgId       Int?
  eventId      Int?
  createdAt    DateTime? @default(now())
  emId         Int?
  cusId        Int?
  auctionPrice Float?

  // Relations
  auctionSta AuctionSta? @relation(fields: [auctionStaId], references: [auctionStaId])
  assetGroup AssetGroup? @relation(fields: [assgId], references: [assgId])
  event      Event?      @relation(fields: [eventId], references: [eventId])
  employee   Employee?   @relation(fields: [emId], references: [emId])
  customer   Customer?   @relation(fields: [cusId], references: [cusId])

  @@map("auctions")
}