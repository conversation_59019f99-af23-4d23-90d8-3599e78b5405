import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting seed...');

  // Clear existing data first
  console.log('🗑️ Clearing existing data...');
  await prisma.auction.deleteMany();
  await prisma.event.deleteMany();
  await prisma.announceDetail.deleteMany();
  await prisma.announce.deleteMany();
  await prisma.assetGroup.deleteMany();
  await prisma.estimate.deleteMany();
  await prisma.asset.deleteMany();
  await prisma.case.deleteMany();
  await prisma.subdistrict.deleteMany();
  await prisma.district.deleteMany();
  await prisma.province.deleteMany();
  await prisma.announceDetailSta.deleteMany();
  await prisma.announceSta.deleteMany();
  await prisma.groupType.deleteMany();
  await prisma.assetGroupSta.deleteMany();
  await prisma.auctionSta.deleteMany();
  await prisma.customer.deleteMany();
  await prisma.employee.deleteMany();
  await prisma.debtor.deleteMany();
  await prisma.creditor.deleteMany();
  await prisma.caseColor.deleteMany();
  await prisma.caseType.deleteMany();
  await prisma.court.deleteMany();
  await prisma.assetType.deleteMany();
  await prisma.assetSta.deleteMany();
  await prisma.assessor.deleteMany();
  await prisma.owner.deleteMany();
  await prisma.user.deleteMany();

  // 1. User = ผู้ใช้งานระบบ
  console.log('Creating Users...');
  const users = await Promise.all([
    prisma.user.create({
      data: {
        id: 1,
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'password123',
      },
    }),
    prisma.user.create({
      data: {
        id: 2,
        name: 'Manager User',
        email: '<EMAIL>',
        password: 'password123',
      },
    }),
    prisma.user.create({
      data: {
        id: 3,
        name: 'Staff User',
        email: '<EMAIL>',
        password: 'password123',
      },
    }),
  ]);

  // 2. Owner = ผู้ถือครองทรัพย์สิน
  console.log('Creating Owners...');
  const owners = await Promise.all([
    prisma.owner.create({
      data: {
        ownerId: 1,
        ownerName: 'บริษัท สยามพาณิชย์ จำกัด',
        ownerTel: '02-123-4567',
        ownerCode: 'OWN001',
      },
    }),
    prisma.owner.create({
      data: {
        ownerId: 2,
        ownerName: 'นายสมชาย ใจดี',
        ownerTel: '************',
        ownerCode: 'OWN002',
      },
    }),
    prisma.owner.create({
      data: {
        ownerId: 3,
        ownerName: 'นางสาวสมหญิง รักดี',
        ownerTel: '************',
        ownerCode: 'OWN003',
      },
    }),
  ]);

  // 3. Assessor = ผู้ประเมินราคาทรัพย์สิน
  console.log('Creating Assessors...');
  const assessors = await Promise.all([
    prisma.assessor.create({
      data: {
        assessorId: 1,
        assessorName: 'นายประเมิน ชำนาญการ',
        assessorTel: '02-987-6543',
        assessorCode: 'ASS001',
      },
    }),
    prisma.assessor.create({
      data: {
        assessorId: 2,
        assessorName: 'นางสาวประเมิน เก่งมาก',
        assessorTel: '************',
        assessorCode: 'ASS002',
      },
    }),
  ]);

  // 4. AssetSta = สถานะ ขายแล้ว/ยังไม่ขาย
  console.log('Creating Asset Status...');
  const assetStatuses = await Promise.all([
    prisma.assetSta.create({
      data: {
        assetsStaName: 'ยังไม่ขาย',
        assetsStaCode: 'NOT_SOLD',
      },
    }),
    prisma.assetSta.create({
      data: {
        assetsStaName: 'ขายแล้ว',
        assetsStaCode: 'SOLD',
      },
    }),
    prisma.assetSta.create({
      data: {
        assetsStaName: 'ระงับการขาย',
        assetsStaCode: 'SUSPENDED',
      },
    }),
  ]);

  // 5. AssetType = ประเภททรัพย์สิน
  console.log('Creating Asset Types...');
  const assetTypes = await Promise.all([
    prisma.assetType.create({
      data: {
        assetTypeName: 'ที่ดิน',
        assetTypeCode: 'LAND',
      },
    }),
    prisma.assetType.create({
      data: {
        assetTypeName: 'บ้านเดี่ยว',
        assetTypeCode: 'HOUSE',
      },
    }),
    prisma.assetType.create({
      data: {
        assetTypeName: 'คอนโดมิเนียม',
        assetTypeCode: 'CONDO',
      },
    }),
    prisma.assetType.create({
      data: {
        assetTypeName: 'อาคารพาณิชย์',
        assetTypeCode: 'COMMERCIAL',
      },
    }),
  ]);

  // 6. Court = ศาล
  console.log('Creating Courts...');
  const courts = await Promise.all([
    prisma.court.create({
      data: {
        courtName: 'ศาลแพ่งกรุงเทพใต้',
        courtAddress: '123 ถนนสาทร แขวงยานนาวา เขตสาทร กรุงเทพมหานคร 10120',
        courtCode: 'CRT001',
      },
    }),
    prisma.court.create({
      data: {
        courtName: 'ศาลแพ่งธนบุรี',
        courtAddress:
          '456 ถนนกรุงธนบุรี แขวงคลองต้นไทร เขตคลองสาน กรุงเทพมหานคร 10600',
        courtCode: 'CRT002',
      },
    }),
  ]);

  // 7. CaseType = ประเภทคดี
  console.log('Creating Case Types...');
  const caseTypes = await Promise.all([
    prisma.caseType.create({
      data: {
        caseTypeName: 'คดีแพ่ง',
        caseTypeCode: 'CIVIL',
      },
    }),
    prisma.caseType.create({
      data: {
        caseTypeName: 'คดีล้มละลาย',
        caseTypeCode: 'BANKRUPTCY',
      },
    }),
    prisma.caseType.create({
      data: {
        caseTypeName: 'คดีบังคับคดี',
        caseTypeCode: 'EXECUTION',
      },
    }),
  ]);

  // 8. CaseColor = สีของคดี แดง/ดำ
  console.log('Creating Case Colors...');
  const caseColors = await Promise.all([
    prisma.caseColor.create({
      data: {
        caseColorName: 'คดีแดง',
        caseColorCode: 'RED',
      },
    }),
    prisma.caseColor.create({
      data: {
        caseColorName: 'คดีดำ',
        caseColorCode: 'BLACK',
      },
    }),
  ]);

  // 9. Creditor = เจ้าหนี้
  console.log('Creating Creditors...');
  const creditors = await Promise.all([
    prisma.creditor.create({
      data: {
        credName: 'ธนาคารกรุงเทพ จำกัด (มหาชน)',
        credTel: '02-626-4000',
        credCode: 'CRED001',
      },
    }),
    prisma.creditor.create({
      data: {
        credName: 'ธนาคารกสิกรไทย จำกัด (มหาชน)',
        credTel: '02-888-8888',
        credCode: 'CRED002',
      },
    }),
    prisma.creditor.create({
      data: {
        credName: 'บริษัท เงินกู้ดี จำกัด',
        credTel: '02-555-1234',
        credCode: 'CRED003',
      },
    }),
  ]);

  // 10. Debtor = ลูกหนี้
  console.log('Creating Debtors...');
  const debtors = await Promise.all([
    prisma.debtor.create({
      data: {
        debtorName: 'นายหนี้สิน มากมาย',
        debtorTel: '************',
        debtorCode: 'DEBT001',
      },
    }),
    prisma.debtor.create({
      data: {
        debtorName: 'นางสาวเงินหมด ไม่มีตัง',
        debtorTel: '************',
        debtorCode: 'DEBT002',
      },
    }),
    prisma.debtor.create({
      data: {
        debtorName: 'บริษัท ล้มละลาย จำกัด',
        debtorTel: '02-777-8888',
        debtorCode: 'DEBT003',
      },
    }),
  ]);

  // 11. Employee = พนักงาน
  console.log('Creating Employees...');
  const employees = await Promise.all([
    prisma.employee.create({
      data: {
        emName: 'นายพนักงาน ขยันมาก',
        emTel: '************',
        emCode: 'EMP001',
      },
    }),
    prisma.employee.create({
      data: {
        emName: 'นางสาวพนักงาน เก่งดี',
        emTel: '************',
        emCode: 'EMP002',
      },
    }),
  ]);

  // 12. Customer = ลูกค้า
  console.log('Creating Customers...');
  const customers = await Promise.all([
    prisma.customer.create({
      data: {
        cusName: 'นายลูกค้า รวยมาก',
        cusTel: '************',
        cusCode: 'CUS001',
      },
    }),
    prisma.customer.create({
      data: {
        cusName: 'นางสาวลูกค้า มีเงิน',
        cusTel: '************',
        cusCode: 'CUS002',
      },
    }),
    prisma.customer.create({
      data: {
        cusName: 'บริษัท ลูกค้าใหญ่ จำกัด',
        cusTel: '02-333-4444',
        cusCode: 'CUS003',
      },
    }),
  ]);

  // 13. AuctionSta = สถานะการประมูล
  console.log('Creating Auction Status...');
  const auctionStatuses = await Promise.all([
    prisma.auctionSta.create({
      data: {
        auctionStaName: 'รอการประมูล',
        auctionStaCode: 'PENDING',
      },
    }),
    prisma.auctionSta.create({
      data: {
        auctionStaName: 'กำลังประมูล',
        auctionStaCode: 'ONGOING',
      },
    }),
    prisma.auctionSta.create({
      data: {
        auctionStaName: 'ประมูลสำเร็จ',
        auctionStaCode: 'SUCCESS',
      },
    }),
    prisma.auctionSta.create({
      data: {
        auctionStaName: 'ประมูลไม่สำเร็จ',
        auctionStaCode: 'FAILED',
      },
    }),
  ]);

  // 14. AssetGroupSta = สถานะกลุ่มทรัพย์สิน
  console.log('Creating Asset Group Status...');
  const assetGroupStatuses = await Promise.all([
    prisma.assetGroupSta.create({
      data: {
        assgStaName: 'รอการจัดกลุ่ม',
        assgStaCode: 'PENDING',
      },
    }),
    prisma.assetGroupSta.create({
      data: {
        assgStaName: 'จัดกลุ่มแล้ว',
        assgStaCode: 'GROUPED',
      },
    }),
    prisma.assetGroupSta.create({
      data: {
        assgStaName: 'ยกเลิกการจัดกลุ่ม',
        assgStaCode: 'CANCELLED',
      },
    }),
  ]);

  // 15. GroupType = ประเภทของกลุ่มทรัพย์สิน
  console.log('Creating Group Types...');
  const groupTypes = await Promise.all([
    prisma.groupType.create({
      data: {
        gtName: 'กลุ่มที่ดิน',
        gtCode: 'LAND_GROUP',
      },
    }),
    prisma.groupType.create({
      data: {
        gtName: 'กลุ่มอสังหาริมทรัพย์',
        gtCode: 'REAL_ESTATE',
      },
    }),
    prisma.groupType.create({
      data: {
        gtName: 'กลุ่มรวม',
        gtCode: 'MIXED',
      },
    }),
  ]);

  // 16. AnnounceSta = สถานะการประกาศ
  console.log('Creating Announce Status...');
  const announceStatuses = await Promise.all([
    prisma.announceSta.create({
      data: {
        anStaName: 'ร่างประกาศ',
        anStaCode: 'DRAFT',
      },
    }),
    prisma.announceSta.create({
      data: {
        anStaName: 'ประกาศแล้ว',
        anStaCode: 'PUBLISHED',
      },
    }),
    prisma.announceSta.create({
      data: {
        anStaName: 'ยกเลิกประกาศ',
        anStaCode: 'CANCELLED',
      },
    }),
  ]);

  // 17. AnnounceDetailSta = สถานะรายละเอียดการประกาศ
  console.log('Creating Announce Detail Status...');
  const announceDetailStatuses = await Promise.all([
    prisma.announceDetailSta.create({
      data: {
        andtStaName: 'รอการอนุมัติ',
        andtStaCode: 'PENDING',
      },
    }),
    prisma.announceDetailSta.create({
      data: {
        andtStaName: 'อนุมัติแล้ว',
        andtStaCode: 'APPROVED',
      },
    }),
    prisma.announceDetailSta.create({
      data: {
        andtStaName: 'ปฏิเสธ',
        andtStaCode: 'REJECTED',
      },
    }),
  ]);

  // 18. Province = จังหวัด
  console.log('Creating Provinces...');
  const provinces = await Promise.all([
    prisma.province.create({
      data: {
        provinceName: 'กรุงเทพมหานคร',
        provinceCode: 'BKK',
      },
    }),
    prisma.province.create({
      data: {
        provinceName: 'นนทบุรี',
        provinceCode: 'NTB',
      },
    }),
    prisma.province.create({
      data: {
        provinceName: 'ปทุมธานี',
        provinceCode: 'PTE',
      },
    }),
  ]);

  // 19. District = อำเภอ
  console.log('Creating Districts...');
  const districts = await Promise.all([
    prisma.district.create({
      data: {
        districtName: 'เขตสาทร',
        districtCode: 'SAT',
        provinceId: provinces[0].provinceId, // กรุงเทพมหานคร
      },
    }),
    prisma.district.create({
      data: {
        districtName: 'เขตคลองสาน',
        districtCode: 'KLS',
        provinceId: provinces[0].provinceId, // กรุงเทพมหานคร
      },
    }),
    prisma.district.create({
      data: {
        districtName: 'อำเภอเมืองนนทบุรี',
        districtCode: 'MNT',
        provinceId: provinces[1].provinceId, // นนทบุรี
      },
    }),
  ]);

  // 20. Subdistrict = ตำบล
  console.log('Creating Subdistricts...');
  const subdistricts = await Promise.all([
    prisma.subdistrict.create({
      data: {
        subdistrictName: 'แขวงยานนาวา',
        subdistrictCode: 'YNN',
        districtId: districts[0].districtId, // เขตสาทร
      },
    }),
    prisma.subdistrict.create({
      data: {
        subdistrictName: 'แขวงคลองต้นไทร',
        subdistrictCode: 'KTT',
        districtId: districts[1].districtId, // เขตคลองสาน
      },
    }),
    prisma.subdistrict.create({
      data: {
        subdistrictName: 'ตำบลสวนใหญ่',
        subdistrictCode: 'SWY',
        districtId: districts[2].districtId, // อำเภอเมืองนนทบุรี
      },
    }),
  ]);

  // 21. Case = คดี
  console.log('Creating Cases...');
  const cases = await Promise.all([
    prisma.case.create({
      data: {
        caseNumber: 'แพ่ง 1234/2567',
        courtId: courts[0].courtId,
        credId: creditors[0].credId,
        debtorId: debtors[0].debtorId,
        caseTypeId: caseTypes[0].caseTypeId,
        caseSummary: 'คดีหนี้สินจากการกู้ยืมเงินธนาคาร',
        caseDate: new Date('2024-01-15'),
        caseColorId: caseColors[0].caseColorId,
      },
    }),
    prisma.case.create({
      data: {
        caseNumber: 'ล้มละลาย 5678/2567',
        courtId: courts[1].courtId,
        credId: creditors[1].credId,
        debtorId: debtors[1].debtorId,
        caseTypeId: caseTypes[1].caseTypeId,
        caseSummary: 'คดีล้มละลายบริษัท',
        caseDate: new Date('2024-02-20'),
        caseColorId: caseColors[1].caseColorId,
      },
    }),
  ]);

  // 22. Asset = ทรัพย์สิน
  console.log('Creating Assets...');
  const assets = await Promise.all([
    prisma.asset.create({
      data: {
        assetName: 'บ้านเดี่ยว 2 ชั้น ซอยสุขุมวิท 39',
        assetDetail: 'บ้านเดี่ยว 2 ชั้น พื้นที่ 200 ตร.ม. ที่ดิน 50 ตร.วา',
        assetLocation: '123 ซอยสุขุมวิท 39 แขวงคลองตัน เขตวัฒนา',
        assetTypeId: assetTypes[1].assetTypeId, // บ้านเดี่ยว
        ownerId: owners[0].ownerId,
        caseId: cases[0].caseId,
        em_id: employees[0].emId,
        assetStaId: assetStatuses[0].assetsStaId, // ยังไม่ขาย
        subdistrictId: subdistricts[0].subdistrictId,
        districtId: districts[0].districtId,
        provinceId: provinces[0].provinceId,
        assetRemark: 'ทรัพย์สินในสภาพดี พร้อมอยู่อาศัย',
      },
    }),
    prisma.asset.create({
      data: {
        assetName: 'ที่ดินเปล่า ถนนรามคำแหง',
        assetDetail: 'ที่ดินเปล่า พื้นที่ 100 ตร.วา หน้ากว้าง 20 เมตร',
        assetLocation: '456 ถนนรามคำแหง แขวงหัวหมาก เขตบางกะปิ',
        assetTypeId: assetTypes[0].assetTypeId, // ที่ดิน
        ownerId: owners[1].ownerId,
        caseId: cases[1].caseId,
        em_id: employees[1].emId,
        assetStaId: assetStatuses[0].assetsStaId, // ยังไม่ขาย
        subdistrictId: subdistricts[1].subdistrictId,
        districtId: districts[1].districtId,
        provinceId: provinces[0].provinceId,
        assetRemark: 'ที่ดินติดถนนใหญ่ เหมาะสำหรับการลงทุน',
      },
    }),
  ]);

  // 23. Estimate = ประเมินราคาทรัพย์สิน
  console.log('Creating Estimates...');
  const estimates = await Promise.all([
    prisma.estimate.create({
      data: {
        esPrice: 8500000.0, // 8.5 ล้านบาท
        esDate: new Date('2024-03-01'),
        assessorId: assessors[0].assessorId,
        assetId: assets[0].assetId,
      },
    }),
    prisma.estimate.create({
      data: {
        esPrice: 12000000.0, // 12 ล้านบาท
        esDate: new Date('2024-03-15'),
        assessorId: assessors[1].assessorId,
        assetId: assets[1].assetId,
      },
    }),
  ]);

  // 24. AssetGroup = กลุ่มของทรัพย์สินที่นำมารวมกันก่อนขาย
  console.log('Creating Asset Groups...');
  const assetGroups = await Promise.all([
    prisma.assetGroup.create({
      data: {
        assetId: assets[0].assetId,
        gtId: groupTypes[1].gtId, // กลุ่มอสังหาริมทรัพย์
        assgStaId: assetGroupStatuses[1].assgStaId, // จัดกลุ่มแล้ว
        assgDetail: 'กลุ่มทรัพย์สินอสังหาริมทรัพย์ในเขตกรุงเทพฯ',
        assgRemark: 'ทรัพย์สินคุณภาพดี เหมาะสำหรับการประมูล',
      },
    }),
    prisma.assetGroup.create({
      data: {
        assetId: assets[1].assetId,
        gtId: groupTypes[0].gtId, // กลุ่มที่ดิน
        assgStaId: assetGroupStatuses[1].assgStaId, // จัดกลุ่มแล้ว
        assgDetail: 'กลุ่มที่ดินเปล่าในเขตกรุงเทพฯ',
        assgRemark: 'ที่ดินในทำเลดี เหมาะสำหรับการลงทุน',
      },
    }),
  ]);

  // 25. Announce = ประกาศ
  console.log('Creating Announces...');
  const announces = await Promise.all([
    prisma.announce.create({
      data: {
        anTitle: 'ประกาศขายทอดตลาดทรัพย์สิน ครั้งที่ 1/2567',
        anDescription: 'ประกาศขายทอดตลาดทรัพย์สินจากคดีแพ่ง รวม 2 รายการ',
        anStaId: announceStatuses[1].anStaId, // ประกาศแล้ว
        em_id: employees[0].emId,
        anRemark: 'ประกาศตามกฎหมาย ครบถ้วนถูกต้อง',
      },
    }),
  ]);

  // 26. AnnounceDetail = รายละเอียดการประกาศ
  console.log('Creating Announce Details...');
  const announceDetails = await Promise.all([
    prisma.announceDetail.create({
      data: {
        assgId: assetGroups[0].assgId,
        andtStaId: announceDetailStatuses[1].andtStaId, // อนุมัติแล้ว
        anId: announces[0].anId,
      },
    }),
    prisma.announceDetail.create({
      data: {
        assgId: assetGroups[1].assgId,
        andtStaId: announceDetailStatuses[1].andtStaId, // อนุมัติแล้ว
        anId: announces[0].anId,
      },
    }),
  ]);

  // 27. Event = กิจกรรมการประมูลสามารถกำหนดได้หลายรอบต่อ 1 การประกาศ
  console.log('Creating Events...');
  const events = await Promise.all([
    prisma.event.create({
      data: {
        eventDate: new Date('2024-04-15T10:00:00Z'),
        anId: announces[0].anId,
        eventRound: 1,
      },
    }),
    prisma.event.create({
      data: {
        eventDate: new Date('2024-05-15T10:00:00Z'),
        anId: announces[0].anId,
        eventRound: 2,
      },
    }),
  ]);

  // 28. Auction = ลูกค้าผู้ชนะการประมูลทรัพย์สิน
  console.log('Creating Auctions...');
  const auctions = await Promise.all([
    prisma.auction.create({
      data: {
        auctionStaId: auctionStatuses[2].auctionStaId, // ประมูลสำเร็จ
        assgId: assetGroups[0].assgId,
        eventId: events[0].eventId,
        emId: employees[0].emId,
        cusId: customers[0].cusId,
        auctionPrice: 9200000.0, // 9.2 ล้านบาท (สูงกว่าราคาประเมิน)
      },
    }),
    prisma.auction.create({
      data: {
        auctionStaId: auctionStatuses[3].auctionStaId, // ประมูลไม่สำเร็จ
        assgId: assetGroups[1].assgId,
        eventId: events[0].eventId,
        emId: employees[1].emId,
        cusId: customers[1].cusId,
        auctionPrice: 10500000.0, // 10.5 ล้านบาท (ต่ำกว่าราคาประเมิน)
      },
    }),
    prisma.auction.create({
      data: {
        auctionStaId: auctionStatuses[2].auctionStaId, // ประมูลสำเร็จ
        assgId: assetGroups[1].assgId,
        eventId: events[1].eventId, // รอบที่ 2
        emId: employees[1].emId,
        cusId: customers[2].cusId,
        auctionPrice: 13500000.0, // 13.5 ล้านบาท (สูงกว่าราคาประเมิน)
      },
    }),
  ]);

  // Reset auto-increment sequences to start from the next available ID
  console.log('🔄 Resetting auto-increment sequences...');

  // Reset sequences for PostgreSQL
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('users', 'id'), COALESCE(MAX(id), 1)) FROM users;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('owners', 'owner_id'), COALESCE(MAX(owner_id), 1)) FROM owners;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('assessors', 'assessor_id'), COALESCE(MAX(assessor_id), 1)) FROM assessors;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('asset_sta', 'assets_sta_id'), COALESCE(MAX(assets_sta_id), 1)) FROM asset_sta;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('asset_types', 'asset_type_id'), COALESCE(MAX(asset_type_id), 1)) FROM asset_types;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('courts', 'court_id'), COALESCE(MAX(court_id), 1)) FROM courts;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('case_types', 'case_type_id'), COALESCE(MAX(case_type_id), 1)) FROM case_types;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('case_colors', 'case_color_id'), COALESCE(MAX(case_color_id), 1)) FROM case_colors;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('creditors', 'cred_id'), COALESCE(MAX(cred_id), 1)) FROM creditors;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('debtors', 'debtor_id'), COALESCE(MAX(debtor_id), 1)) FROM debtors;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('employees', 'em_id'), COALESCE(MAX(em_id), 1)) FROM employees;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('customers', 'cus_id'), COALESCE(MAX(cus_id), 1)) FROM customers;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('auction_sta', 'auction_sta_id'), COALESCE(MAX(auction_sta_id), 1)) FROM auction_sta;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('asset_group_sta', 'assg_sta_id'), COALESCE(MAX(assg_sta_id), 1)) FROM asset_group_sta;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('group_types', 'gt_id'), COALESCE(MAX(gt_id), 1)) FROM group_types;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('announce_sta', 'an_sta_id'), COALESCE(MAX(an_sta_id), 1)) FROM announce_sta;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('announce_detail_sta', 'andt_sta_id'), COALESCE(MAX(andt_sta_id), 1)) FROM announce_detail_sta;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('provinces', 'province_id'), COALESCE(MAX(province_id), 1)) FROM provinces;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('districts', 'district_id'), COALESCE(MAX(district_id), 1)) FROM districts;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('subdistricts', 'subdistrict_id'), COALESCE(MAX(subdistrict_id), 1)) FROM subdistricts;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('cases', 'case_id'), COALESCE(MAX(case_id), 1)) FROM cases;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('assets', 'asset_id'), COALESCE(MAX(asset_id), 1)) FROM assets;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('estimates', 'es_id'), COALESCE(MAX(es_id), 1)) FROM estimates;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('asset_groups', 'assg_id'), COALESCE(MAX(assg_id), 1)) FROM asset_groups;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('announces', 'an_id'), COALESCE(MAX(an_id), 1)) FROM announces;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('announce_details', 'andt_id'), COALESCE(MAX(andt_id), 1)) FROM announce_details;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('events', 'event_id'), COALESCE(MAX(event_id), 1)) FROM events;`;
  await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('auctions', 'auction_id'), COALESCE(MAX(auction_id), 1)) FROM auctions;`;

  console.log('✅ Seed completed successfully!');
  console.log(`Created:
  - ${users.length} Users
  - ${owners.length} Owners
  - ${assessors.length} Assessors
  - ${assetStatuses.length} Asset Statuses
  - ${assetTypes.length} Asset Types
  - ${courts.length} Courts
  - ${caseTypes.length} Case Types
  - ${caseColors.length} Case Colors
  - ${creditors.length} Creditors
  - ${debtors.length} Debtors
  - ${employees.length} Employees
  - ${customers.length} Customers
  - ${auctionStatuses.length} Auction Statuses
  - ${assetGroupStatuses.length} Asset Group Statuses
  - ${groupTypes.length} Group Types
  - ${announceStatuses.length} Announce Statuses
  - ${announceDetailStatuses.length} Announce Detail Statuses
  - ${provinces.length} Provinces
  - ${districts.length} Districts
  - ${subdistricts.length} Subdistricts
  - ${cases.length} Cases
  - ${assets.length} Assets
  - ${estimates.length} Estimates
  - ${assetGroups.length} Asset Groups
  - ${announces.length} Announces
  - ${announceDetails.length} Announce Details
  - ${events.length} Events
  - ${auctions.length} Auctions

🎯 All auto-increment sequences have been reset to continue from the next available ID.`);
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
