import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // 🔄 ล้างข้อมูลก่อน (แนะนำเฉพาะ dev)
  await prisma.tasks.deleteMany();
  await prisma.projects.deleteMany();
  await prisma.users.deleteMany();
  await prisma.status.deleteMany();

  // ✅ Seed Status
  const statusNames = ['TODO', 'IN_PROGRESS', 'DONE'];
  const statusMap: Record<string, number> = {};

  for (const name of statusNames) {
    const status = await prisma.status.create({ data: { name } });
    statusMap[name] = status.id;
  }
  console.log('✅ Seeded statuses');

  // 👤 Seed Users
  const alice = await prisma.users.create({
    data: {
      name: 'Alice',
      email: '<EMAIL>',
    },
  });

  const bob = await prisma.users.create({
    data: {
      name: '<PERSON>',
      email: '<EMAIL>',
    },
  });

  console.log('✅ Seeded users');

  // 📁 Seed Projects + Tasks
  await prisma.projects.create({
    data: {
      name: 'Alice Project',
      ownerId: alice.id,
      tasks: {
        create: [
          {
            name: 'Design UI',
            description: 'Create wireframes and mockups',
            statusId: statusMap['TODO'],
          },
          {
            name: 'Setup Backend',
            description: 'NestJS + Prisma + PostgreSQL',
            statusId: statusMap['IN_PROGRESS'],
          },
        ],
      },
    },
  });

  await prisma.projects.create({
    data: {
      name: 'Bob Project',
      ownerId: bob.id,
      tasks: {
        create: [
          {
            name: 'Write Documentation',
            description: 'README and API Reference',
            statusId: statusMap['DONE'],
          },
          {
            name: 'Deploy',
            description: 'Docker + CI/CD setup',
            statusId: statusMap['TODO'],
          },
        ],
      },
    },
  });

  console.log('✅ Seeded projects and tasks');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding data:', e);
    process.exit(1);
  })
  .finally(async () => {
    prisma.$disconnect();
  });
