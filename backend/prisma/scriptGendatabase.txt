USE [master]
GO
/****** Object:  Database [PropertyTrading]    Script Date: 7/10/2025 1:50:15 PM ******/
CREATE DATABASE [PropertyTrading]
 CONTAINMENT = NONE
 ON  PRIMARY 
( NAME = N'PropertyTrading', FILENAME = N'C:\Users\<USER>\PropertyTrading.mdf' , SIZE = 8192KB , MAXSIZE = UNLIMITED, FILEGROWTH = 65536KB )
 LOG ON 
( NAME = N'PropertyTrading_log', FILENAME = N'C:\Users\<USER>\PropertyTrading_log.ldf' , SIZE = 8192KB , MAXSIZE = 2048GB , FILEGROWTH = 65536KB )
 WITH CATALOG_COLLATION = DATABASE_DEFAULT
GO
ALTER DATABASE [PropertyTrading] SET COMPATIBILITY_LEVEL = 150
GO
IF (1 = FULLTEXTSERVICEPROPERTY('IsFullTextInstalled'))
begin
EXEC [PropertyTrading].[dbo].[sp_fulltext_database] @action = 'enable'
end
GO
ALTER DATABASE [PropertyTrading] SET ANSI_NULL_DEFAULT OFF 
GO
ALTER DATABASE [PropertyTrading] SET ANSI_NULLS OFF 
GO
ALTER DATABASE [PropertyTrading] SET ANSI_PADDING OFF 
GO
ALTER DATABASE [PropertyTrading] SET ANSI_WARNINGS OFF 
GO
ALTER DATABASE [PropertyTrading] SET ARITHABORT OFF 
GO
ALTER DATABASE [PropertyTrading] SET AUTO_CLOSE OFF 
GO
ALTER DATABASE [PropertyTrading] SET AUTO_SHRINK OFF 
GO
ALTER DATABASE [PropertyTrading] SET AUTO_UPDATE_STATISTICS ON 
GO
ALTER DATABASE [PropertyTrading] SET CURSOR_CLOSE_ON_COMMIT OFF 
GO
ALTER DATABASE [PropertyTrading] SET CURSOR_DEFAULT  GLOBAL 
GO
ALTER DATABASE [PropertyTrading] SET CONCAT_NULL_YIELDS_NULL OFF 
GO
ALTER DATABASE [PropertyTrading] SET NUMERIC_ROUNDABORT OFF 
GO
ALTER DATABASE [PropertyTrading] SET QUOTED_IDENTIFIER OFF 
GO
ALTER DATABASE [PropertyTrading] SET RECURSIVE_TRIGGERS OFF 
GO
ALTER DATABASE [PropertyTrading] SET  DISABLE_BROKER 
GO
ALTER DATABASE [PropertyTrading] SET AUTO_UPDATE_STATISTICS_ASYNC OFF 
GO
ALTER DATABASE [PropertyTrading] SET DATE_CORRELATION_OPTIMIZATION OFF 
GO
ALTER DATABASE [PropertyTrading] SET TRUSTWORTHY OFF 
GO
ALTER DATABASE [PropertyTrading] SET ALLOW_SNAPSHOT_ISOLATION OFF 
GO
ALTER DATABASE [PropertyTrading] SET PARAMETERIZATION SIMPLE 
GO
ALTER DATABASE [PropertyTrading] SET READ_COMMITTED_SNAPSHOT OFF 
GO
ALTER DATABASE [PropertyTrading] SET HONOR_BROKER_PRIORITY OFF 
GO
ALTER DATABASE [PropertyTrading] SET RECOVERY SIMPLE 
GO
ALTER DATABASE [PropertyTrading] SET  MULTI_USER 
GO
ALTER DATABASE [PropertyTrading] SET PAGE_VERIFY CHECKSUM  
GO
ALTER DATABASE [PropertyTrading] SET DB_CHAINING OFF 
GO
ALTER DATABASE [PropertyTrading] SET FILESTREAM( NON_TRANSACTED_ACCESS = OFF ) 
GO
ALTER DATABASE [PropertyTrading] SET TARGET_RECOVERY_TIME = 60 SECONDS 
GO
ALTER DATABASE [PropertyTrading] SET DELAYED_DURABILITY = DISABLED 
GO
ALTER DATABASE [PropertyTrading] SET ACCELERATED_DATABASE_RECOVERY = OFF  
GO
ALTER DATABASE [PropertyTrading] SET QUERY_STORE = OFF
GO
USE [PropertyTrading]
GO
/****** Object:  Table [dbo].[announceDetails]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[announceDetails](
	[andtId] [int] IDENTITY(1,1) NOT NULL,
	[assgId] [int] NULL,
	[andtStaId] [int] NULL,
	[CreatedAt] [datetime] NULL,
	[anId] [int] NULL,
 CONSTRAINT [PK_announceDetails] PRIMARY KEY CLUSTERED 
(
	[andtId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[announceDetailSta]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[announceDetailSta](
	[andtStaId] [int] IDENTITY(1,1) NOT NULL,
	[andtStaName] [nvarchar](50) NOT NULL,
	[andtStaCode] [nvarchar](20) NULL,
 CONSTRAINT [PK_announceDetailSta] PRIMARY KEY CLUSTERED 
(
	[andtStaId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[announces]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[announces](
	[anId] [int] IDENTITY(1,1) NOT NULL,
	[anTitle] [nvarchar](100) NULL,
	[anDescription] [nvarchar](500) NULL,
	[CreatedAt] [datetime] NULL,
	[anStaId] [int] NULL,
	[em_id] [int] NULL,
	[anRemark] [nvarchar](300) NULL,
 CONSTRAINT [PK_auctionAnnouncements] PRIMARY KEY CLUSTERED 
(
	[anId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[announceSta]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[announceSta](
	[anStaId] [int] IDENTITY(1,1) NOT NULL,
	[anStaName] [nvarchar](50) NOT NULL,
	[anStaCode] [nvarchar](20) NULL,
 CONSTRAINT [PK_announceSta] PRIMARY KEY CLUSTERED 
(
	[anStaId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[assessors]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[assessors](
	[assessorId] [int] IDENTITY(1,1) NOT NULL,
	[assessorName] [nvarchar](100) NOT NULL,
	[assessorTel] [varchar](20) NOT NULL,
	[assessorCode] [varchar](50) NULL,
 CONSTRAINT [PK_assessors] PRIMARY KEY CLUSTERED 
(
	[assessorId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[assetGroups]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[assetGroups](
	[assgId] [int] IDENTITY(1,1) NOT NULL,
	[assetId] [int] NULL,
	[gtId] [int] NULL,
	[CreatedAt] [datetime] NULL,
	[assgStaId] [int] NULL,
	[assgDetail] [nvarchar](500) NULL,
	[assgRemark] [nvarchar](200) NULL,
 CONSTRAINT [PK_assetGroups] PRIMARY KEY CLUSTERED 
(
	[assgId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[assetGroupSta]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[assetGroupSta](
	[assgStaId] [int] IDENTITY(1,1) NOT NULL,
	[assgStaName] [nvarchar](50) NOT NULL,
	[assgStaCode] [nvarchar](20) NULL,
 CONSTRAINT [PK_assetGroupSta] PRIMARY KEY CLUSTERED 
(
	[assgStaId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[assets]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[assets](
	[assetId] [int] IDENTITY(1,1) NOT NULL,
	[assetName] [nvarchar](100) NULL,
	[assetDetail] [nvarchar](500) NULL,
	[assetLocation] [nvarchar](200) NULL,
	[assetTypeId] [int] NULL,
	[ownerId] [int] NULL,
	[CreatedAt] [datetime] NULL,
	[caseId] [int] NULL,
	[em_id] [int] NULL,
	[assetStaId] [int] NULL,
	[subdistrictId] [int] NULL,
	[districtId] [int] NULL,
	[provinceId] [int] NULL,
	[assetRemark] [nvarchar](200) NULL,
 CONSTRAINT [PK_assets] PRIMARY KEY CLUSTERED 
(
	[assetId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[assetSta]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[assetSta](
	[assetsStaId] [int] IDENTITY(1,1) NOT NULL,
	[assetsStaName] [nvarchar](50) NULL,
	[assetsStaCode] [nvarchar](20) NULL,
 CONSTRAINT [PK_assetSta] PRIMARY KEY CLUSTERED 
(
	[assetsStaId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[assetTypes]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[assetTypes](
	[assetTypeId] [int] IDENTITY(1,1) NOT NULL,
	[assetTypeName] [nvarchar](50) NOT NULL,
	[assetTypeCode] [nvarchar](30) NULL,
 CONSTRAINT [PK_assetTypes] PRIMARY KEY CLUSTERED 
(
	[assetTypeId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[auctions]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[auctions](
	[auctionId] [int] IDENTITY(1,1) NOT NULL,
	[auctionStaId] [int] NULL,
	[assgId] [int] NULL,
	[eventId] [int] NULL,
	[CreatedAt] [datetime] NULL,
	[emId] [int] NULL,
	[cusId] [int] NULL,
	[auctionPrice] [float] NULL,
 CONSTRAINT [PK_auctions] PRIMARY KEY CLUSTERED 
(
	[auctionId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[auctionSta]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[auctionSta](
	[auctionStaId] [int] IDENTITY(1,1) NOT NULL,
	[auctionStaName] [nvarchar](50) NULL,
	[auctionStaCode] [varchar](50) NULL,
 CONSTRAINT [PK_auctionSta] PRIMARY KEY CLUSTERED 
(
	[auctionStaId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[caseColors]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[caseColors](
	[caseColorId] [int] IDENTITY(1,1) NOT NULL,
	[caseColorName] [nvarchar](50) NULL,
	[caseColorCode] [varchar](20) NULL,
 CONSTRAINT [PK_caseColors] PRIMARY KEY CLUSTERED 
(
	[caseColorId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[cases]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[cases](
	[caseId] [int] IDENTITY(1,1) NOT NULL,
	[caseNumber] [nvarchar](50) NOT NULL,
	[courtId] [int] NULL,
	[credId] [int] NULL,
	[debtorId] [int] NULL,
	[caseTypeId] [int] NULL,
	[caseSummary] [nvarchar](500) NULL,
	[caseDate] [datetime] NULL,
	[CreatedAt] [datetime] NULL,
	[caseColorId] [int] NULL,
 CONSTRAINT [PK_cases] PRIMARY KEY CLUSTERED 
(
	[caseId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[caseTypes]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[caseTypes](
	[caseTypeId] [int] IDENTITY(1,1) NOT NULL,
	[caseTypeName] [nvarchar](50) NULL,
	[caseTypeCode] [nvarchar](20) NULL,
 CONSTRAINT [PK_caseTypes] PRIMARY KEY CLUSTERED 
(
	[caseTypeId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[courts]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[courts](
	[courtId] [int] IDENTITY(1,1) NOT NULL,
	[courtName] [nvarchar](100) NULL,
	[courtAddress] [nvarchar](300) NULL,
	[courtCode] [nvarchar](20) NULL,
 CONSTRAINT [PK_courts] PRIMARY KEY CLUSTERED 
(
	[courtId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[creditors]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[creditors](
	[credId] [int] IDENTITY(1,1) NOT NULL,
	[credName] [varchar](100) NOT NULL,
	[credTel] [varchar](20) NULL,
	[credCode] [varchar](50) NULL,
 CONSTRAINT [PK_creditors] PRIMARY KEY CLUSTERED 
(
	[credId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[customers]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[customers](
	[cusId] [int] IDENTITY(1,1) NOT NULL,
	[cusName] [nvarchar](100) NOT NULL,
	[cusTel] [varchar](20) NOT NULL,
	[cusCode] [varchar](50) NULL,
 CONSTRAINT [PK_customers] PRIMARY KEY CLUSTERED 
(
	[cusId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[debtors]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[debtors](
	[debtorId] [int] IDENTITY(1,1) NOT NULL,
	[debtorName] [nvarchar](100) NOT NULL,
	[debtorTel] [nvarchar](50) NOT NULL,
	[debtorCode] [varchar](50) NOT NULL,
 CONSTRAINT [PK_debtors] PRIMARY KEY CLUSTERED 
(
	[debtorId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[districts]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[districts](
	[districtId] [int] IDENTITY(1,1) NOT NULL,
	[districtName] [nvarchar](80) NULL,
	[districtCode] [nvarchar](20) NULL,
	[provinceId] [int] NULL,
 CONSTRAINT [PK_districts] PRIMARY KEY CLUSTERED 
(
	[districtId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[employees]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[employees](
	[emId] [int] IDENTITY(1,1) NOT NULL,
	[emName] [nvarchar](100) NOT NULL,
	[emTel] [varchar](20) NOT NULL,
	[emCode] [varchar](50) NULL,
 CONSTRAINT [PK_employees] PRIMARY KEY CLUSTERED 
(
	[emId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[estimates]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[estimates](
	[esId] [int] IDENTITY(1,1) NOT NULL,
	[esPrice] [float] NULL,
	[esDate] [datetime] NULL,
	[assessorId] [int] NULL,
	[assetId] [int] NULL,
	[CreatedAt] [datetime] NULL,
 CONSTRAINT [PK_estimates] PRIMARY KEY CLUSTERED 
(
	[esId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[events]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[events](
	[eventId] [int] IDENTITY(1,1) NOT NULL,
	[eventDate] [datetime] NULL,
	[CreatedAt] [datetime] NULL,
	[anId] [int] NULL,
	[eventRound] [int] NULL,
 CONSTRAINT [PK_events] PRIMARY KEY CLUSTERED 
(
	[eventId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[groupTypes]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[groupTypes](
	[gtId] [int] IDENTITY(1,1) NOT NULL,
	[gtName] [nvarchar](20) NULL,
	[gtCode] [nvarchar](20) NULL,
 CONSTRAINT [PK_groupTypes] PRIMARY KEY CLUSTERED 
(
	[gtId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[owners]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[owners](
	[ownerId] [int] IDENTITY(1,1) NOT NULL,
	[ownerName] [nvarchar](100) NOT NULL,
	[ownerTel] [varchar](20) NOT NULL,
	[ownerCode] [varchar](50) NULL,
 CONSTRAINT [PK_owners] PRIMARY KEY CLUSTERED 
(
	[ownerId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[provinces]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[provinces](
	[provinceId] [int] IDENTITY(1,1) NOT NULL,
	[provinceName] [nvarchar](100) NULL,
	[provinceCode] [nvarchar](20) NULL,
 CONSTRAINT [PK_provinces] PRIMARY KEY CLUSTERED 
(
	[provinceId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[subdistricts]    Script Date: 7/10/2025 1:50:15 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[subdistricts](
	[subdistrictId] [int] IDENTITY(1,1) NOT NULL,
	[subdistrictName] [nvarchar](80) NULL,
	[subdistrictCode] [nvarchar](20) NULL,
	[districtId] [int] NULL,
 CONSTRAINT [PK_subdistricts] PRIMARY KEY CLUSTERED 
(
	[subdistrictId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[announceDetails]  WITH CHECK ADD  CONSTRAINT [FK_announceDetails_announceDetailSta] FOREIGN KEY([andtStaId])
REFERENCES [dbo].[announceDetailSta] ([andtStaId])
GO
ALTER TABLE [dbo].[announceDetails] CHECK CONSTRAINT [FK_announceDetails_announceDetailSta]
GO
ALTER TABLE [dbo].[announceDetails]  WITH CHECK ADD  CONSTRAINT [FK_announceDetails_announces] FOREIGN KEY([anId])
REFERENCES [dbo].[announces] ([anId])
GO
ALTER TABLE [dbo].[announceDetails] CHECK CONSTRAINT [FK_announceDetails_announces]
GO
ALTER TABLE [dbo].[announceDetails]  WITH CHECK ADD  CONSTRAINT [FK_announceDetails_assetGroups] FOREIGN KEY([assgId])
REFERENCES [dbo].[assetGroups] ([assgId])
GO
ALTER TABLE [dbo].[announceDetails] CHECK CONSTRAINT [FK_announceDetails_assetGroups]
GO
ALTER TABLE [dbo].[announces]  WITH CHECK ADD  CONSTRAINT [FK_announces_announceSta] FOREIGN KEY([anStaId])
REFERENCES [dbo].[announceSta] ([anStaId])
GO
ALTER TABLE [dbo].[announces] CHECK CONSTRAINT [FK_announces_announceSta]
GO
ALTER TABLE [dbo].[announces]  WITH CHECK ADD  CONSTRAINT [FK_announces_employees] FOREIGN KEY([em_id])
REFERENCES [dbo].[employees] ([emId])
GO
ALTER TABLE [dbo].[announces] CHECK CONSTRAINT [FK_announces_employees]
GO
ALTER TABLE [dbo].[assetGroups]  WITH CHECK ADD  CONSTRAINT [FK_assetGroups_assetGroupSta] FOREIGN KEY([assgStaId])
REFERENCES [dbo].[assetGroupSta] ([assgStaId])
GO
ALTER TABLE [dbo].[assetGroups] CHECK CONSTRAINT [FK_assetGroups_assetGroupSta]
GO
ALTER TABLE [dbo].[assetGroups]  WITH CHECK ADD  CONSTRAINT [FK_assetGroups_assets] FOREIGN KEY([assetId])
REFERENCES [dbo].[assets] ([assetId])
GO
ALTER TABLE [dbo].[assetGroups] CHECK CONSTRAINT [FK_assetGroups_assets]
GO
ALTER TABLE [dbo].[assetGroups]  WITH CHECK ADD  CONSTRAINT [FK_assetGroups_groupTypes] FOREIGN KEY([gtId])
REFERENCES [dbo].[groupTypes] ([gtId])
GO
ALTER TABLE [dbo].[assetGroups] CHECK CONSTRAINT [FK_assetGroups_groupTypes]
GO
ALTER TABLE [dbo].[assets]  WITH CHECK ADD  CONSTRAINT [FK_assets_assetSta] FOREIGN KEY([assetStaId])
REFERENCES [dbo].[assetSta] ([assetsStaId])
GO
ALTER TABLE [dbo].[assets] CHECK CONSTRAINT [FK_assets_assetSta]
GO
ALTER TABLE [dbo].[assets]  WITH CHECK ADD  CONSTRAINT [FK_assets_assetTypes] FOREIGN KEY([assetTypeId])
REFERENCES [dbo].[assetTypes] ([assetTypeId])
GO
ALTER TABLE [dbo].[assets] CHECK CONSTRAINT [FK_assets_assetTypes]
GO
ALTER TABLE [dbo].[assets]  WITH CHECK ADD  CONSTRAINT [FK_assets_cases] FOREIGN KEY([caseId])
REFERENCES [dbo].[cases] ([caseId])
GO
ALTER TABLE [dbo].[assets] CHECK CONSTRAINT [FK_assets_cases]
GO
ALTER TABLE [dbo].[assets]  WITH CHECK ADD  CONSTRAINT [FK_assets_districts] FOREIGN KEY([districtId])
REFERENCES [dbo].[districts] ([districtId])
GO
ALTER TABLE [dbo].[assets] CHECK CONSTRAINT [FK_assets_districts]
GO
ALTER TABLE [dbo].[assets]  WITH CHECK ADD  CONSTRAINT [FK_assets_employees] FOREIGN KEY([em_id])
REFERENCES [dbo].[employees] ([emId])
GO
ALTER TABLE [dbo].[assets] CHECK CONSTRAINT [FK_assets_employees]
GO
ALTER TABLE [dbo].[assets]  WITH CHECK ADD  CONSTRAINT [FK_assets_owners] FOREIGN KEY([ownerId])
REFERENCES [dbo].[owners] ([ownerId])
GO
ALTER TABLE [dbo].[assets] CHECK CONSTRAINT [FK_assets_owners]
GO
ALTER TABLE [dbo].[assets]  WITH CHECK ADD  CONSTRAINT [FK_assets_provinces] FOREIGN KEY([provinceId])
REFERENCES [dbo].[provinces] ([provinceId])
GO
ALTER TABLE [dbo].[assets] CHECK CONSTRAINT [FK_assets_provinces]
GO
ALTER TABLE [dbo].[assets]  WITH CHECK ADD  CONSTRAINT [FK_assets_subdistricts] FOREIGN KEY([subdistrictId])
REFERENCES [dbo].[subdistricts] ([subdistrictId])
GO
ALTER TABLE [dbo].[assets] CHECK CONSTRAINT [FK_assets_subdistricts]
GO
ALTER TABLE [dbo].[auctions]  WITH CHECK ADD  CONSTRAINT [FK_auctions_assetGroups] FOREIGN KEY([assgId])
REFERENCES [dbo].[assetGroups] ([assgId])
GO
ALTER TABLE [dbo].[auctions] CHECK CONSTRAINT [FK_auctions_assetGroups]
GO
ALTER TABLE [dbo].[auctions]  WITH CHECK ADD  CONSTRAINT [FK_auctions_auctionSta] FOREIGN KEY([auctionStaId])
REFERENCES [dbo].[auctionSta] ([auctionStaId])
GO
ALTER TABLE [dbo].[auctions] CHECK CONSTRAINT [FK_auctions_auctionSta]
GO
ALTER TABLE [dbo].[auctions]  WITH CHECK ADD  CONSTRAINT [FK_auctions_customers] FOREIGN KEY([cusId])
REFERENCES [dbo].[customers] ([cusId])
GO
ALTER TABLE [dbo].[auctions] CHECK CONSTRAINT [FK_auctions_customers]
GO
ALTER TABLE [dbo].[auctions]  WITH CHECK ADD  CONSTRAINT [FK_auctions_employees] FOREIGN KEY([emId])
REFERENCES [dbo].[employees] ([emId])
GO
ALTER TABLE [dbo].[auctions] CHECK CONSTRAINT [FK_auctions_employees]
GO
ALTER TABLE [dbo].[auctions]  WITH CHECK ADD  CONSTRAINT [FK_auctions_events] FOREIGN KEY([eventId])
REFERENCES [dbo].[events] ([eventId])
GO
ALTER TABLE [dbo].[auctions] CHECK CONSTRAINT [FK_auctions_events]
GO
ALTER TABLE [dbo].[cases]  WITH CHECK ADD  CONSTRAINT [FK_cases_caseColors] FOREIGN KEY([caseColorId])
REFERENCES [dbo].[caseColors] ([caseColorId])
GO
ALTER TABLE [dbo].[cases] CHECK CONSTRAINT [FK_cases_caseColors]
GO
ALTER TABLE [dbo].[cases]  WITH CHECK ADD  CONSTRAINT [FK_cases_caseTypes] FOREIGN KEY([caseTypeId])
REFERENCES [dbo].[caseTypes] ([caseTypeId])
GO
ALTER TABLE [dbo].[cases] CHECK CONSTRAINT [FK_cases_caseTypes]
GO
ALTER TABLE [dbo].[cases]  WITH CHECK ADD  CONSTRAINT [FK_cases_courts] FOREIGN KEY([courtId])
REFERENCES [dbo].[courts] ([courtId])
GO
ALTER TABLE [dbo].[cases] CHECK CONSTRAINT [FK_cases_courts]
GO
ALTER TABLE [dbo].[cases]  WITH CHECK ADD  CONSTRAINT [FK_cases_creditors] FOREIGN KEY([credId])
REFERENCES [dbo].[creditors] ([credId])
GO
ALTER TABLE [dbo].[cases] CHECK CONSTRAINT [FK_cases_creditors]
GO
ALTER TABLE [dbo].[cases]  WITH CHECK ADD  CONSTRAINT [FK_cases_debtors] FOREIGN KEY([debtorId])
REFERENCES [dbo].[debtors] ([debtorId])
GO
ALTER TABLE [dbo].[cases] CHECK CONSTRAINT [FK_cases_debtors]
GO
ALTER TABLE [dbo].[districts]  WITH CHECK ADD  CONSTRAINT [FK_districts_provinces] FOREIGN KEY([provinceId])
REFERENCES [dbo].[provinces] ([provinceId])
GO
ALTER TABLE [dbo].[districts] CHECK CONSTRAINT [FK_districts_provinces]
GO
ALTER TABLE [dbo].[estimates]  WITH CHECK ADD  CONSTRAINT [FK_estimates_assessors] FOREIGN KEY([assessorId])
REFERENCES [dbo].[assessors] ([assessorId])
GO
ALTER TABLE [dbo].[estimates] CHECK CONSTRAINT [FK_estimates_assessors]
GO
ALTER TABLE [dbo].[estimates]  WITH CHECK ADD  CONSTRAINT [FK_estimates_assets] FOREIGN KEY([assetId])
REFERENCES [dbo].[assets] ([assetId])
GO
ALTER TABLE [dbo].[estimates] CHECK CONSTRAINT [FK_estimates_assets]
GO
ALTER TABLE [dbo].[events]  WITH CHECK ADD  CONSTRAINT [FK_events_announces] FOREIGN KEY([anId])
REFERENCES [dbo].[announces] ([anId])
GO
ALTER TABLE [dbo].[events] CHECK CONSTRAINT [FK_events_announces]
GO
ALTER TABLE [dbo].[subdistricts]  WITH CHECK ADD  CONSTRAINT [FK_subdistricts_districts] FOREIGN KEY([districtId])
REFERENCES [dbo].[districts] ([districtId])
GO
ALTER TABLE [dbo].[subdistricts] CHECK CONSTRAINT [FK_subdistricts_districts]
GO
USE [master]
GO
ALTER DATABASE [PropertyTrading] SET  READ_WRITE 
GO
